import { describe, it, expect, vi, beforeEach } from "vitest";

import { BacktestEngine } from "@/lib/backtest";
import type {
  FundData,
  IndexData,
  Fund,
  FixedAmountParameters,
  GridTradingParameters,
} from "@/types/fund";

// Mock data
const mockFundData: FundData[] = [
  { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
  { date: "2024-01-02", netAssetValue: 1.01, accumulatedValue: 1.01 },
  { date: "2024-01-03", netAssetValue: 0.995, accumulatedValue: 0.995 },
  { date: "2024-01-04", netAssetValue: 1.02, accumulatedValue: 1.02 },
  { date: "2024-01-05", netAssetValue: 1.015, accumulatedValue: 1.015 },
];

const mockIndexData: IndexData[] = [
  { date: "2024-01-01", value: 3000, change: 0 },
  { date: "2024-01-02", value: 3030, change: 1 },
  { date: "2024-01-03", value: 2985, change: -1.5 },
  { date: "2024-01-04", value: 3060, change: 2.5 },
  { date: "2024-01-05", value: 3045, change: -0.5 },
];

const mockFund: Fund = {
  id: "test_fund",
  name: "测试基金",
  code: "000001",
  type: "stock",
  indexId: "test_index",
};

describe("BacktestEngine", () => {
  let engine: BacktestEngine;

  beforeEach(() => {
    engine = new BacktestEngine(mockFundData, mockIndexData);
  });

  describe("构造函数", () => {
    it("应该正确初始化回测引擎", () => {
      expect(engine).toBeInstanceOf(BacktestEngine);
    });

    it("应该对数据按日期排序", () => {
      const unsortedData: FundData[] = [
        { date: "2024-01-03", netAssetValue: 0.995, accumulatedValue: 0.995 },
        { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
        { date: "2024-01-02", netAssetValue: 1.01, accumulatedValue: 1.01 },
      ];

      const testEngine = new BacktestEngine(unsortedData);
      // 通过运行回测来验证数据已排序
      expect(() => testEngine).not.toThrow();
    });
  });

  describe("runBacktest", () => {
    it("应该执行定投策略回测", async () => {
      const parameters: FixedAmountParameters = {
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        initialAmount: 1000,
        monthlyAmount: 500,
        frequency: "monthly",
      };

      const result = await engine.runBacktest(mockFund, parameters);

      expect(result).toBeDefined();
      expect(result.strategy).toBe("fixed_amount");
      expect(result.fund).toEqual(mockFund);
      expect(result.params).toEqual(parameters);
      expect(result.performance).toBeDefined();
      expect(result.timeline).toBeDefined();
      expect(Array.isArray(result.timeline)).toBe(true);
    });

    it("应该执行网格交易策略回测", async () => {
      const parameters: GridTradingParameters = {
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        initialAmount: 10_000,
        gridCount: 5,
        priceRange: { min: 0.95, max: 1.05 },
        investmentPerGrid: 2000,
        rebalanceFrequency: "daily",
      };

      const result = await engine.runBacktest(mockFund, parameters);

      expect(result).toBeDefined();
      expect(result.strategy).toBe("grid_trading");
      expect(result.timeline.length).toBeGreaterThan(0);
    });
  });

  describe("calculatePerformance", () => {
    it("应该正确计算投资表现指标", () => {
      const timeline = [
        {
          date: "2024-01-01",
          investment: 1000,
          totalInvestment: 1000,
          shares: 1000,
          value: 1000,
          return: 0,
          netAssetValue: 1,
        },
        {
          date: "2024-01-02",
          investment: 500,
          totalInvestment: 1500,
          shares: 1495.05,
          value: 1509.6,
          return: 0.64,
          netAssetValue: 1.01,
        },
      ];

      // 使用反射访问私有方法进行测试
      const performance = (engine as any).calculatePerformance(timeline);

      expect(performance).toBeDefined();
      expect(performance.totalInvestment).toBe(1500);
      expect(performance.finalValue).toBe(1509.6);
      expect(performance.totalReturn).toBeCloseTo(0.64, 2);
      expect(performance.annualizedReturn).toBeDefined();
      expect(performance.maxDrawdown).toBeDefined();
      expect(performance.volatility).toBeDefined();
      expect(performance.sharpeRatio).toBeDefined();
    });

    it("应该处理空时间线", () => {
      const performance = (engine as any).calculatePerformance([]);

      expect(performance.totalInvestment).toBe(0);
      expect(performance.finalValue).toBe(0);
      expect(performance.totalReturn).toBe(0);
    });
  });

  describe("getNavOnDate", () => {
    it("应该返回指定日期的净值", () => {
      const nav = (engine as any).getNavOnDate(new Date("2024-01-02"));
      expect(nav).toBeDefined();
      expect(nav.netAssetValue).toBe(1.01);
    });

    it("应该处理不存在的日期", () => {
      const nav = (engine as any).getNavOnDate(new Date("2024-12-31"));
      expect(nav).toBeNull();
    });
  });

  describe("calculateFixedAmountInvestment", () => {
    it("应该计算定投金额", () => {
      const parameters: FixedAmountParameters = {
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        initialAmount: 1000,
        monthlyAmount: 500,
        frequency: "monthly",
      };

      const timeline = [
        {
          date: "2024-01-01",
          investment: 1000,
          totalInvestment: 1000,
          shares: 1000,
          value: 1000,
          return: 0,
          netAssetValue: 1,
        },
      ];

      // 在开始日期，应该返回0（因为初始投资已经在timeline中处理）
      const investmentOnStart = (engine as any).calculateFixedAmountInvestment(
        parameters,
        new Date("2024-01-01"),
        timeline
      );
      expect(investmentOnStart).toBe(0);

      // 在后续日期，应该返回月投金额
      const investmentLater = (engine as any).calculateFixedAmountInvestment(
        parameters,
        new Date("2024-02-01"),
        timeline
      );
      expect(investmentLater).toBe(500);
    });
  });

  describe("generatePerformanceComparison", () => {
    it("应该生成性能对比数据", () => {
      const timeline = [
        {
          date: "2024-01-01",
          investment: 1000,
          totalInvestment: 1000,
          shares: 1000,
          value: 1000,
          return: 0,
          netAssetValue: 1,
        },
        {
          date: "2024-01-02",
          investment: 0,
          totalInvestment: 1000,
          shares: 1000,
          value: 1010,
          return: 1,
          netAssetValue: 1.01,
        },
      ];

      const comparison = (engine as any).generatePerformanceComparison(
        timeline
      );

      expect(comparison).toBeDefined();
      expect(Array.isArray(comparison)).toBe(true);
      expect(comparison.length).toBe(2);

      expect(comparison[0]).toHaveProperty("date");
      expect(comparison[0]).toHaveProperty("strategyReturn");
      expect(comparison[0]).toHaveProperty("fundReturn");
      expect(comparison[0]).toHaveProperty("indexReturn");
    });
  });

  describe("边界情况处理", () => {
    it("应该处理空的基金数据", () => {
      const emptyEngine = new BacktestEngine([]);
      expect(() => emptyEngine).not.toThrow();
    });

    it("应该处理单日数据", () => {
      const singleDayData: FundData[] = [
        { date: "2024-01-01", netAssetValue: 1, accumulatedValue: 1 },
      ];

      const singleDayEngine = new BacktestEngine(singleDayData);
      expect(() => singleDayEngine).not.toThrow();
    });

    it("应该处理无效的日期范围", async () => {
      const parameters: FixedAmountParameters = {
        startDate: "2024-12-31", // 超出数据范围
        endDate: "2024-12-31",
        initialAmount: 1000,
        monthlyAmount: 500,
        frequency: "monthly",
      };

      const result = await engine.runBacktest(mockFund, parameters);
      expect(result.timeline.length).toBe(0);
    });
  });

  describe("策略类型识别", () => {
    it("应该正确识别定投策略", () => {
      const parameters: FixedAmountParameters = {
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        initialAmount: 1000,
        monthlyAmount: 500,
        frequency: "monthly",
      };

      const strategyType = (engine as any).getStrategyType(parameters);
      expect(strategyType).toBe("fixed_amount");
    });

    it("应该正确识别网格交易策略", () => {
      const parameters: GridTradingParameters = {
        startDate: "2024-01-01",
        endDate: "2024-01-05",
        initialAmount: 10_000,
        gridCount: 5,
        priceRange: { min: 0.95, max: 1.05 },
        investmentPerGrid: 2000,
        rebalanceFrequency: "daily",
      };

      const strategyType = (engine as any).getStrategyType(parameters);
      expect(strategyType).toBe("grid_trading");
    });
  });
});
