import { describe, it, expect } from "vitest";

import type {
  BaiduApiResponse,
  BaiduApiResult,
  BaiduApiSeries,
  ParsedFundDataPoint,
  PerformanceDataPoint,
  PerformanceComparisonData,
  FundApiParameters,
  ApiError,
  FundBasicInfo,
  CacheItem,
  ApiStatus,
  DataFetchOptions,
  BaiduIndexApiResponse,
  ParsedIndexDataPoint,
  IndexApiParameters,
  CSIndexApiResponse,
  CSIndexDataPoint,
  CSIndexListRequest,
  CSIndexListResponse,
  CSIndexListItem,
} from "@/lib/api/types";

describe("API Types", () => {
  describe("BaiduApiResponse", () => {
    it("应该有正确的结构", () => {
      const response: BaiduApiResponse = {
        QueryID: "test-query-id",
        Result: [],
        ResultCode: "200",
        ResultNum: "1",
      };

      expect(response.QueryID).toBe("test-query-id");
      expect(Array.isArray(response.Result)).toBe(true);
      expect(response.ResultCode).toBe("200");
      expect(response.ResultNum).toBe("1");
    });

    it("应该包含完整的Result结构", () => {
      const result: BaiduApiResult = {
        ClickNeed: "test",
        DisplayData: {
          StdStg: "test",
          StdStl: "test",
          resultData: {
            extData: {
              OriginQuery: "test",
              resourceid: "test",
              tplt: "test",
            },
            tplData: {
              ResultURL: "test",
              card_order: "test",
              data_source: "test",
              digits: "test",
              disp_data_url_ex: {
                aesplitid: "test",
              },
              lyAxis: [],
              maxPoints: "test",
              sec: 0,
              series: [],
              showDate: "test",
              showTag: "test",
              text: "test",
              xAxis: [],
            },
          },
          strategy: {
            ctplOrPhp: "test",
            hilightWord: "test",
            precharge: "test",
            tempName: "test",
          },
        },
        OriginSrcID: "test",
        RecoverCacheTime: "test",
        ResultURL: "test",
        Sort: "test",
        SrcID: "test",
        SubResNum: "test",
        SubResult: [],
        Weight: "test",
      };

      expect(result.DisplayData.resultData.tplData.series).toBeDefined();
      expect(Array.isArray(result.DisplayData.resultData.tplData.series)).toBe(
        true
      );
    });
  });

  describe("BaiduApiSeries", () => {
    it("应该有正确的结构", () => {
      const series: BaiduApiSeries = {
        label: ["label1", "label2"],
        name: "净值曲线",
        value: "2024-01-01,1.0000,0.00%,1.0000;2024-01-02,1.0100,1.00%,1.0100",
        special: "test",
      };

      expect(Array.isArray(series.label)).toBe(true);
      expect(typeof series.name).toBe("string");
      expect(typeof series.value).toBe("string");
      expect(series.special).toBe("test");
    });
  });

  describe("ParsedFundDataPoint", () => {
    it("应该有正确的结构", () => {
      const dataPoint: ParsedFundDataPoint = {
        date: "2024-01-01",
        netAssetValue: 1,
        dailyChange: "0.00%",
        accumulatedValue: 1,
      };

      expect(typeof dataPoint.date).toBe("string");
      expect(typeof dataPoint.netAssetValue).toBe("number");
      expect(typeof dataPoint.dailyChange).toBe("string");
      expect(typeof dataPoint.accumulatedValue).toBe("number");
    });
  });

  describe("PerformanceDataPoint", () => {
    it("应该有正确的结构", () => {
      const dataPoint: PerformanceDataPoint = {
        date: "2024-01-01",
        return: 5,
      };

      expect(typeof dataPoint.date).toBe("string");
      expect(typeof dataPoint.return).toBe("number");
    });
  });

  describe("PerformanceComparisonData", () => {
    it("应该有正确的结构", () => {
      const comparisonData: PerformanceComparisonData = {
        fund: [{ date: "2024-01-01", return: 5 }],
        benchmark: [{ date: "2024-01-01", return: 4 }],
        index: [{ date: "2024-01-01", return: 3 }],
      };

      expect(Array.isArray(comparisonData.fund)).toBe(true);
      expect(Array.isArray(comparisonData.benchmark)).toBe(true);
      expect(Array.isArray(comparisonData.index)).toBe(true);
    });
  });

  describe("FundApiParameters", () => {
    it("应该有正确的结构", () => {
      const parameters: FundApiParameters = {
        fundCode: "000001",
        startDate: "2024-01-01",
        endDate: "2024-12-31",
        dataType: "nvl",
        months: 12,
        source: "qieman",
      };

      expect(typeof parameters.fundCode).toBe("string");
      expect(parameters.dataType).toBe("nvl");
      expect(typeof parameters.months).toBe("number");
    });

    it("应该支持可选参数", () => {
      const minimalParameters: FundApiParameters = {
        fundCode: "000001",
      };

      expect(minimalParameters.fundCode).toBe("000001");
      expect(minimalParameters.startDate).toBeUndefined();
      expect(minimalParameters.endDate).toBeUndefined();
    });

    it("应该限制dataType的值", () => {
      const nvlParameters: FundApiParameters = {
        fundCode: "000001",
        dataType: "nvl",
      };

      const aiParameters: FundApiParameters = {
        fundCode: "000001",
        dataType: "ai",
      };

      expect(nvlParameters.dataType).toBe("nvl");
      expect(aiParameters.dataType).toBe("ai");
    });
  });

  describe("ApiError", () => {
    it("应该有正确的结构", () => {
      const error: ApiError = {
        code: "INVALID_FUND_CODE",
        message: "无效的基金代码",
        details: { fundCode: "000001" },
      };

      expect(typeof error.code).toBe("string");
      expect(typeof error.message).toBe("string");
      expect(error.details).toBeDefined();
    });

    it("应该支持可选的details", () => {
      const simpleError: ApiError = {
        code: "NETWORK_ERROR",
        message: "网络连接失败",
      };

      expect(simpleError.details).toBeUndefined();
    });
  });

  describe("FundBasicInfo", () => {
    it("应该有正确的结构", () => {
      const info: FundBasicInfo = {
        code: "000001",
        name: "华夏成长混合",
        type: "混合型",
        manager: "华夏基金",
        establishDate: "2001-12-18",
        scale: "100亿",
        description: "基金描述",
      };

      expect(typeof info.code).toBe("string");
      expect(typeof info.name).toBe("string");
      expect(typeof info.type).toBe("string");
      expect(typeof info.manager).toBe("string");
      expect(typeof info.establishDate).toBe("string");
    });

    it("应该支持可选字段", () => {
      const minimalInfo: FundBasicInfo = {
        code: "000001",
        name: "华夏成长混合",
        type: "混合型",
        manager: "华夏基金",
        establishDate: "2001-12-18",
      };

      expect(minimalInfo.scale).toBeUndefined();
      expect(minimalInfo.description).toBeUndefined();
    });
  });

  describe("CacheItem", () => {
    it("应该有正确的结构", () => {
      const cacheItem: CacheItem<string> = {
        data: "cached data",
        timestamp: Date.now(),
        ttl: 5 * 60 * 1000,
      };

      expect(typeof cacheItem.data).toBe("string");
      expect(typeof cacheItem.timestamp).toBe("number");
      expect(typeof cacheItem.ttl).toBe("number");
    });

    it("应该支持泛型", () => {
      const stringCache: CacheItem<string> = {
        data: "string data",
        timestamp: Date.now(),
        ttl: 1000,
      };

      const objectCache: CacheItem<{ test: string }> = {
        data: { test: "object data" },
        timestamp: Date.now(),
        ttl: 1000,
      };

      expect(typeof stringCache.data).toBe("string");
      expect(typeof objectCache.data).toBe("object");
    });
  });

  describe("ApiStatus", () => {
    it("应该包含所有状态值", () => {
      const statuses: ApiStatus[] = ["idle", "loading", "success", "error"];

      for (const status of statuses) {
        const testStatus: ApiStatus = status;
        expect(["idle", "loading", "success", "error"]).toContain(testStatus);
      }
    });
  });

  describe("DataFetchOptions", () => {
    it("应该有正确的结构", () => {
      const options: DataFetchOptions = {
        useCache: true,
        timeout: 10_000,
        retryCount: 3,
        retryDelay: 1000,
      };

      expect(typeof options.useCache).toBe("boolean");
      expect(typeof options.timeout).toBe("number");
      expect(typeof options.retryCount).toBe("number");
      expect(typeof options.retryDelay).toBe("number");
    });

    it("应该支持所有可选参数", () => {
      const emptyOptions: DataFetchOptions = {};

      expect(emptyOptions.useCache).toBeUndefined();
      expect(emptyOptions.timeout).toBeUndefined();
      expect(emptyOptions.retryCount).toBeUndefined();
      expect(emptyOptions.retryDelay).toBeUndefined();
    });
  });

  describe("BaiduIndexApiResponse", () => {
    it("应该有正确的结构", () => {
      const response: BaiduIndexApiResponse = {
        ResultCode: 0,
        ResultNum: 1,
        QueryID: "test-query",
        Result: {
          newMarketData: {
            headers: ["timestamp", "open", "close"],
            keys: ["key1", "key2"],
            marketData: "1640995200,3000,3100;1641081600,3100,3200",
          },
        },
      };

      expect(typeof response.ResultCode).toBe("number");
      expect(typeof response.ResultNum).toBe("number");
      expect(typeof response.QueryID).toBe("string");
      expect(response.Result.newMarketData).toBeDefined();
    });
  });

  describe("ParsedIndexDataPoint", () => {
    it("应该有正确的结构", () => {
      const dataPoint: ParsedIndexDataPoint = {
        timestamp: 1_640_995_200_000,
        time: "2024-01-01",
        open: 3000,
        close: 3100,
        volume: 1_000_000,
        high: 3150,
        low: 2950,
        amount: 50_000_000,
        range: 100,
        ratio: 3.33,
        turnoverratio: "2.5%",
        preClose: 3000,
        ma5avgprice: 3050,
        ma5volume: 1_200_000,
        ma10avgprice: 3025,
        ma10volume: 1_100_000,
        ma20avgprice: 3000,
        ma20volume: 1_000_000,
      };

      expect(typeof dataPoint.timestamp).toBe("number");
      expect(typeof dataPoint.time).toBe("string");
      expect(typeof dataPoint.open).toBe("number");
      expect(typeof dataPoint.close).toBe("number");
    });

    it("应该支持可选的移动平均字段", () => {
      const minimalDataPoint: ParsedIndexDataPoint = {
        timestamp: 1_640_995_200_000,
        time: "2024-01-01",
        open: 3000,
        close: 3100,
        volume: 1_000_000,
        high: 3150,
        low: 2950,
        amount: 50_000_000,
        range: 100,
        ratio: 3.33,
        turnoverratio: "2.5%",
        preClose: 3000,
      };

      expect(minimalDataPoint.ma5avgprice).toBeUndefined();
      expect(minimalDataPoint.ma5volume).toBeUndefined();
    });
  });

  describe("IndexApiParameters", () => {
    it("应该有正确的结构", () => {
      const parameters: IndexApiParameters = {
        indexCode: "000300",
        ktype: "day",
        count: 100,
        endTime: "2024-01-01",
      };

      expect(typeof parameters.indexCode).toBe("string");
      expect(parameters.ktype).toBe("day");
      expect(typeof parameters.count).toBe("number");
    });

    it("应该限制ktype的值", () => {
      const dayParameters: IndexApiParameters = {
        indexCode: "000300",
        ktype: "day",
      };
      const weekParameters: IndexApiParameters = {
        indexCode: "000300",
        ktype: "week",
      };
      const monthParameters: IndexApiParameters = {
        indexCode: "000300",
        ktype: "month",
      };

      expect(dayParameters.ktype).toBe("day");
      expect(weekParameters.ktype).toBe("week");
      expect(monthParameters.ktype).toBe("month");
    });
  });

  describe("CSIndexApiResponse", () => {
    it("应该有正确的结构", () => {
      const response: CSIndexApiResponse = {
        code: "200",
        msg: "success",
        data: [],
      };

      expect(typeof response.code).toBe("string");
      expect(typeof response.msg).toBe("string");
      expect(Array.isArray(response.data)).toBe(true);
    });
  });

  describe("CSIndexDataPoint", () => {
    it("应该有正确的结构", () => {
      const dataPoint: CSIndexDataPoint = {
        tradeDate: "20240101",
        indexCode: "000300",
        indexNameCnAll: "沪深300指数",
        indexNameCn: "沪深300",
        indexNameEnAll: "CSI 300 Index",
        indexNameEn: "CSI 300",
        open: 3000,
        high: 3150,
        low: 2950,
        close: 3100,
        change: 100,
        changePct: 3.33,
        tradingVol: 1000,
        tradingValue: 50_000,
        consNumber: 300,
        peg: 15.5,
      };

      expect(typeof dataPoint.tradeDate).toBe("string");
      expect(typeof dataPoint.indexCode).toBe("string");
      expect(typeof dataPoint.open).toBe("number");
      expect(typeof dataPoint.close).toBe("number");
    });

    it("应该支持可选的peg字段", () => {
      const dataPointWithoutPeg: CSIndexDataPoint = {
        tradeDate: "20240101",
        indexCode: "000300",
        indexNameCnAll: "沪深300指数",
        indexNameCn: "沪深300",
        indexNameEnAll: "CSI 300 Index",
        indexNameEn: "CSI 300",
        open: 3000,
        high: 3150,
        low: 2950,
        close: 3100,
        change: 100,
        changePct: 3.33,
        tradingVol: 1000,
        tradingValue: 50_000,
        consNumber: 300,
      };

      expect(dataPointWithoutPeg.peg).toBeUndefined();
    });
  });

  describe("CSIndexListRequest", () => {
    it("应该有正确的结构", () => {
      const request: CSIndexListRequest = {
        sorter: {
          sortField: "indexCode",
          sortOrder: "asc",
        },
        pager: {
          pageNum: 1,
          pageSize: 20,
        },
        searchInput: "沪深300",
        indexFilter: {
          ifCustomized: false,
          ifTracked: true,
          ifWeightCapped: null,
          indexCompliance: null,
          hotSpot: null,
          indexClassify: null,
          currency: null,
          region: null,
          indexSeries: null,
          undefined: null,
        },
      };

      expect(request.sorter).toBeDefined();
      expect(request.pager).toBeDefined();
      expect(request.indexFilter).toBeDefined();
    });
  });

  describe("CSIndexListResponse", () => {
    it("应该有正确的结构", () => {
      const response: CSIndexListResponse = {
        code: "200",
        msg: "success",
        data: {
          total: 100,
          list: [],
        },
      };

      expect(typeof response.code).toBe("string");
      expect(typeof response.msg).toBe("string");
      expect(typeof response.data.total).toBe("number");
      expect(Array.isArray(response.data.list)).toBe(true);
    });
  });

  describe("CSIndexListItem", () => {
    it("应该有正确的结构", () => {
      const item: CSIndexListItem = {
        indexCode: "000300",
        indexNameCn: "沪深300",
        indexNameEn: "CSI 300",
        indexNameCnAll: "沪深300指数",
        indexNameEnAll: "CSI 300 Index",
        publishDate: "20050408",
        baseDate: "20041231",
        basePoint: 1000,
      };

      expect(typeof item.indexCode).toBe("string");
      expect(typeof item.indexNameCn).toBe("string");
      expect(typeof item.indexNameEn).toBe("string");
      expect(typeof item.basePoint).toBe("number");
    });
  });
});
