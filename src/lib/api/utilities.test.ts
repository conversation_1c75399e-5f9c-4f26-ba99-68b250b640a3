import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";

import type { BaiduApiResponse, CSIndexApiResponse } from "@/lib/api/types";
import {
  parseFundData,
  parsePerformanceData,
  formatDate,
  buildApiUrl,
  validateFundCode,
  validateApiParameters,
  createApiError,
  CacheManager,
  withRetry,
  withTimeout,
  generateCacheKey,
  validateDateRange,
  formatErrorMessage,
  buildCSIndexApiUrl,
  parseCSIndexData,
  formatDateForCSIndex,
  getCSIndexName,
} from "@/lib/api/utilities";

describe("API Utils", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe("parseFundData", () => {
    const mockResponse: BaiduApiResponse = {
      QueryID: "test-query",
      ResultCode: 0,
      ResultNum: 1,
      Result: [
        {
          DisplayData: {
            resultData: {
              tplData: {
                series: [
                  {
                    name: "净值曲线",
                    value:
                      "2024-01-01,1.0000,0.00%,1.0000;2024-01-02,1.0100,1.00%,1.0100",
                    label: "净值曲线",
                  },
                ],
              },
            },
          },
        },
      ],
    };

    it("应该成功解析基金数据", () => {
      const result = parseFundData(mockResponse);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        date: "2024-01-01",
        netAssetValue: 1,
        dailyChange: "0.00%",
        accumulatedValue: 1,
      });
      expect(result[1]).toEqual({
        date: "2024-01-02",
        netAssetValue: 1.01,
        dailyChange: "1.00%",
        accumulatedValue: 1.01,
      });
    });

    it("应该按日期排序数据", () => {
      const unsortedResponse: BaiduApiResponse = {
        QueryID: "test-query",
        ResultCode: 0,
        ResultNum: 1,
        Result: [
          {
            DisplayData: {
              resultData: {
                tplData: {
                  series: [
                    {
                      name: "净值曲线",
                      value:
                        "2024-01-03,1.0200,2.00%,1.0200;2024-01-01,1.0000,0.00%,1.0000;2024-01-02,1.0100,1.00%,1.0100",
                      label: "净值曲线",
                    },
                  ],
                },
              },
            },
          },
        ],
      };

      const result = parseFundData(unsortedResponse);

      expect(result[0].date).toBe("2024-01-01");
      expect(result[1].date).toBe("2024-01-02");
      expect(result[2].date).toBe("2024-01-03");
    });

    it("应该处理空数据", () => {
      const emptyResponse: BaiduApiResponse = { Result: [] };

      expect(() => parseFundData(emptyResponse)).toThrow("API返回数据为空");
    });

    it("应该处理缺少净值曲线的数据", () => {
      const noNetValueResponse: BaiduApiResponse = {
        Result: [
          {
            DisplayData: {
              resultData: {
                tplData: {
                  series: [
                    {
                      name: "其他数据",
                      value: "2024-01-01,1.0000",
                    },
                  ],
                },
              },
            },
          },
        ],
      };

      expect(() => parseFundData(noNetValueResponse)).toThrow(
        "未找到净值曲线数据"
      );
    });

    it("应该跳过无效的数据条目", () => {
      const invalidDataResponse: BaiduApiResponse = {
        Result: [
          {
            DisplayData: {
              resultData: {
                tplData: {
                  series: [
                    {
                      name: "净值曲线",
                      value:
                        "2024-01-01,1.0000,0.00%,1.0000;;invalid,data;2024-01-02,1.0100,1.00%,1.0100",
                    },
                  ],
                },
              },
            },
          },
        ],
      };

      const result = parseFundData(invalidDataResponse);
      expect(result).toHaveLength(2);
    });
  });

  describe("parsePerformanceData", () => {
    const mockPerformanceResponse: BaiduApiResponse = {
      Result: [
        {
          DisplayData: {
            resultData: {
              tplData: {
                series: [
                  {
                    name: "本基金",
                    value: "2024-01-01,5.00%;2024-01-02,6.00%",
                  },
                  {
                    name: "同类平均",
                    value: "2024-01-01,4.00%;2024-01-02,5.00%",
                  },
                  {
                    name: "沪深300",
                    value: "2024-01-01,3.00%;2024-01-02,4.00%",
                  },
                ],
              },
            },
          },
        },
      ],
    };

    it("应该成功解析业绩数据", () => {
      const result = parsePerformanceData(mockPerformanceResponse);

      expect(result.fund).toHaveLength(2);
      expect(result.benchmark).toHaveLength(2);
      expect(result.index).toHaveLength(2);

      expect(result.fund[0]).toEqual({
        date: "2024-01-01",
        return: 5,
      });
    });

    it("应该处理缺少某些系列的数据", () => {
      const partialResponse: BaiduApiResponse = {
        Result: [
          {
            DisplayData: {
              resultData: {
                tplData: {
                  series: [{ name: "本基金", value: "2024-01-01,5.00%" }],
                },
              },
            },
          },
        ],
      };

      const result = parsePerformanceData(partialResponse);

      expect(result.fund).toHaveLength(1);
      expect(result.benchmark).toHaveLength(0);
      expect(result.index).toHaveLength(0);
    });
  });

  describe("formatDate", () => {
    it("应该保持YYYY-MM-DD格式不变", () => {
      expect(formatDate("2024-01-01")).toBe("2024-01-01");
    });

    it("应该将MM-DD格式转换为当前年份", () => {
      const currentYear = new Date().getFullYear();
      expect(formatDate("01-01")).toBe(`${currentYear}-01-01`);
    });

    it("应该解析其他日期格式", () => {
      expect(formatDate("2024/01/01")).toBe("2024-01-01");
    });

    it("应该处理无效日期", () => {
      expect(() => formatDate("invalid-date")).toThrow("无效的日期格式");
    });
  });

  describe("buildApiUrl", () => {
    it("应该构建基本API URL", () => {
      const url = buildApiUrl("000001");

      expect(url).toContain("https://gushitong.baidu.com/opendata");
      expect(url).toContain("query=000001");
      expect(url).toContain("resource_id=5824");
      expect(url).toContain("m=12");
      expect(url).toContain("t=nvl");
    });

    it("应该支持自定义选项", () => {
      const url = buildApiUrl("000001", {
        dataType: "ai",
        months: 36,
        source: "qieman",
      });

      expect(url).toContain("t=ai");
      expect(url).toContain("m=36");
      expect(url).toContain("source=qieman");
    });

    it("应该只在ai类型时添加source参数", () => {
      const nvlUrl = buildApiUrl("000001", {
        dataType: "nvl",
        source: "qieman",
      });
      const aiUrl = buildApiUrl("000001", { dataType: "ai", source: "qieman" });

      expect(nvlUrl).not.toContain("source=qieman");
      expect(aiUrl).toContain("source=qieman");
    });
  });

  describe("validateFundCode", () => {
    it("应该验证有效的基金代码", () => {
      expect(validateFundCode("000001")).toBe(true);
      expect(validateFundCode("123456")).toBe(true);
    });

    it("应该拒绝无效的基金代码", () => {
      expect(validateFundCode("12345")).toBe(false); // 5位
      expect(validateFundCode("1234567")).toBe(false); // 7位
      expect(validateFundCode("abc123")).toBe(false); // 包含字母
      expect(validateFundCode("")).toBe(false); // 空字符串
    });
  });

  describe("validateApiParameters", () => {
    it("应该验证有效参数", () => {
      const result = validateApiParameters({
        fundCode: "000001",
        dataType: "nvl",
        months: 12,
      });

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it("应该检测无效的基金代码", () => {
      const result = validateApiParameters({
        fundCode: "invalid",
        dataType: "nvl",
      });

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("基金代码格式错误，应为6位数字");
    });

    it("应该检测无效的数据类型", () => {
      const result = validateApiParameters({
        fundCode: "000001",
        dataType: "invalid" as any,
      });

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("数据类型只能是 nvl 或 ai");
    });

    it("应该检测无效的月数", () => {
      const result = validateApiParameters({
        fundCode: "000001",
        months: 13,
      });

      expect(result.valid).toBe(false);
      expect(result.errors[0]).toContain("月数参数无效");
    });

    it("应该要求ai类型有source参数", () => {
      const result = validateApiParameters({
        fundCode: "000001",
        dataType: "ai",
      });

      expect(result.valid).toBe(false);
      expect(result.errors).toContain("ai类型数据需要指定source参数");
    });
  });

  describe("createApiError", () => {
    it("应该创建API错误对象", () => {
      const error = createApiError("INVALID_CODE", "无效的基金代码", {
        code: "000001",
      });

      expect(error.code).toBe("INVALID_CODE");
      expect(error.message).toBe("无效的基金代码");
      expect(error.details).toEqual({ code: "000001" });
    });
  });

  describe("CacheManager", () => {
    let cacheManager: CacheManager;

    beforeEach(() => {
      cacheManager = CacheManager.getInstance();
      cacheManager.clear();
    });

    it("应该是单例模式", () => {
      const instance1 = CacheManager.getInstance();
      const instance2 = CacheManager.getInstance();
      expect(instance1).toBe(instance2);
    });

    it("应该能够设置和获取缓存", () => {
      const data = { test: "data" };
      cacheManager.set("key1", data);

      expect(cacheManager.get("key1")).toEqual(data);
    });

    it("应该在TTL过期后返回null", async () => {
      const data = { test: "data" };
      cacheManager.set("key1", data, 10); // 10ms TTL

      await new Promise((resolve) => setTimeout(resolve, 20));

      expect(cacheManager.get("key1")).toBeNull();
    });

    it("应该能够清除缓存", () => {
      cacheManager.set("key1", "data1");
      cacheManager.set("key2", "data2");

      expect(cacheManager.size()).toBe(2);

      cacheManager.clear();

      expect(cacheManager.size()).toBe(0);
    });
  });

  describe("withRetry", () => {
    it("应该在成功时直接返回结果", async () => {
      const function_ = vi.fn().mockResolvedValue("success");

      const result = await withRetry(function_);

      expect(result).toBe("success");
      expect(function_).toHaveBeenCalledTimes(1);
    });

    it("应该在失败时重试", async () => {
      const function_ = vi
        .fn()
        .mockRejectedValueOnce(new Error("fail1"))
        .mockRejectedValueOnce(new Error("fail2"))
        .mockResolvedValue("success");

      const result = await withRetry(function_, { maxRetries: 3, delay: 1 });

      expect(result).toBe("success");
      expect(function_).toHaveBeenCalledTimes(3);
    });

    it("应该在达到最大重试次数后抛出错误", async () => {
      const function_ = vi.fn().mockRejectedValue(new Error("always fail"));

      await expect(withRetry(function_, { maxRetries: 2, delay: 1 })).rejects.toThrow(
        "always fail"
      );

      expect(function_).toHaveBeenCalledTimes(3); // 1 initial + 2 retries
    });
  });

  describe("withTimeout", () => {
    it("应该在超时前返回结果", async () => {
      const promise = Promise.resolve("success");

      const result = await withTimeout(promise, 1000);

      expect(result).toBe("success");
    });

    it("应该在超时后抛出错误", async () => {
      const promise = new Promise((resolve) => setTimeout(resolve, 100));

      await expect(withTimeout(promise, 10)).rejects.toThrow("请求超时");
    });
  });

  describe("generateCacheKey", () => {
    it("应该生成基本缓存键", () => {
      expect(generateCacheKey("000001")).toBe("000001");
    });

    it("应该包含日期参数", () => {
      expect(generateCacheKey("000001", "2024-01-01", "2024-12-31")).toBe(
        "000001_2024-01-01_2024-12-31"
      );
    });

    it("应该处理部分参数", () => {
      expect(generateCacheKey("000001", "2024-01-01")).toBe(
        "000001_2024-01-01"
      );
    });
  });

  describe("validateDateRange", () => {
    it("应该验证有效的日期范围", () => {
      expect(validateDateRange("2024-01-01", "2024-12-31")).toBe(true);
      expect(validateDateRange("2024-01-01", "2024-01-01")).toBe(true);
    });

    it("应该拒绝无效的日期范围", () => {
      expect(validateDateRange("2024-12-31", "2024-01-01")).toBe(false);
      expect(validateDateRange("invalid", "2024-01-01")).toBe(false);
      expect(validateDateRange("2024-01-01", "invalid")).toBe(false);
    });
  });

  describe("formatErrorMessage", () => {
    it("应该格式化Error对象", () => {
      const error = new Error("Test error");
      expect(formatErrorMessage(error)).toBe("Test error");
    });

    it("应该格式化字符串错误", () => {
      expect(formatErrorMessage("String error")).toBe("String error");
    });

    it("应该处理未知错误类型", () => {
      expect(formatErrorMessage(123)).toBe("未知错误");
      expect(formatErrorMessage(null)).toBe("未知错误");
      expect(formatErrorMessage()).toBe("未知错误");
    });
  });

  describe("buildCSIndexApiUrl", () => {
    it("应该构建中证指数API URL", () => {
      const url = buildCSIndexApiUrl("000300", "2024-01-01", "2024-12-31");

      expect(url).toContain(
        "https://www.csindex.com.cn/csindex-home/perf/index-perf"
      );
      expect(url).toContain("indexCode=000300");
      expect(url).toContain("startDate=2024-01-01");
      expect(url).toContain("endDate=2024-12-31");
    });
  });

  describe("parseCSIndexData", () => {
    const mockCSResponse: CSIndexApiResponse = {
      code: "200",
      msg: "success",
      data: [
        {
          tradeDate: "20240101",
          open: 3000,
          close: 3100,
          high: 3150,
          low: 2950,
          change: 100,
          changePct: 3.33,
          tradingVol: 1000,
          tradingValue: 50_000,
        },
      ],
    };

    it("应该成功解析中证指数数据", () => {
      const result = parseCSIndexData(mockCSResponse);

      expect(result).toHaveLength(1);
      expect(result[0]).toMatchObject({
        time: "2024-01-01",
        open: 3000,
        close: 3100,
        high: 3150,
        low: 2950,
        range: 100,
        ratio: 3.33,
      });
    });

    it("应该处理API错误响应", () => {
      const errorResponse: CSIndexApiResponse = {
        code: "400",
        msg: "Bad Request",
        data: [],
      };

      expect(() => parseCSIndexData(errorResponse)).toThrow(
        "API错误: Bad Request"
      );
    });
  });

  describe("formatDateForCSIndex", () => {
    it("应该将日期格式化为YYYYMMDD", () => {
      expect(formatDateForCSIndex("2024-01-01")).toBe("20240101");
      expect(formatDateForCSIndex("2024-12-31")).toBe("20241231");
    });

    it("应该处理无效日期", () => {
      expect(() => formatDateForCSIndex("invalid")).toThrow(
        "无效的日期: invalid"
      );
    });
  });

  describe("getCSIndexName", () => {
    it("应该返回已知指数的名称", () => {
      expect(getCSIndexName("000300")).toBe("沪深300");
      expect(getCSIndexName("000905")).toBe("中证500");
      expect(getCSIndexName("000001")).toBe("上证综指");
    });

    it("应该为未知指数返回默认名称", () => {
      expect(getCSIndexName("999999")).toBe("指数999999");
    });
  });
});
