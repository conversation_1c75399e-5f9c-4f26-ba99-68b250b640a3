// API类型定义 - 基于实际API响应结构

// 百度股市通API原始响应结构
export interface BaiduApiResponse {
  QueryID: string;
  Result: BaiduApiResult[];
  ResultCode: string;
  ResultNum: string;
}

export interface BaiduApiResult {
  ClickNeed: string;
  DisplayData: {
    StdStg: string;
    StdStl: string;
    resultData: {
      extData: {
        OriginQuery: string;
        resourceid: string;
        tplt: string;
      };
      tplData: {
        ResultURL: string;
        card_order: string;
        data_source: string;
        digits: string;
        disp_data_url_ex: {
          aesplitid: string;
        };
        lyAxis: unknown[];
        maxPoints: string;
        sec: number;
        series: BaiduApiSeries[];
        showDate: string;
        showTag: string;
        text: string;
        xAxis: string[];
      };
    };
    strategy: {
      ctplOrPhp: string;
      hilightWord: string;
      precharge: string;
      tempName: string;
    };
  };
  OriginSrcID: string;
  RecoverCacheTime: string;
  ResultURL: string;
  Sort: string;
  SrcID: string;
  SubResNum: string;
  SubResult: unknown[];
  Weight: string;
}

export interface BaiduApiSeries {
  label: string[];
  name: string;
  value: string; // 格式: "日期,净值,涨幅,累计净值;..." 或 "日期,收益率;..."
  special?: string; // 特殊标记，用于区分不同类型的数据
}

// 解析后的基金数据点
export interface ParsedFundDataPoint {
  date: string;
  netAssetValue: number;
  dailyChange: number;
  accumulatedValue: number;
}

// 业绩对比数据点
export interface PerformanceDataPoint {
  date: string;
  return: number; // 收益率 (%)
}

// 业绩对比数据集合
export interface PerformanceComparisonData {
  fund: PerformanceDataPoint[]; // 本基金
  benchmark: PerformanceDataPoint[]; // 同类平均
  index: PerformanceDataPoint[]; // 指数(如沪深300)
}

// API请求参数
export interface FundApiParameters {
  fundCode: string;
  startDate?: string;
  endDate?: string;
  dataType?: "nvl" | "ai"; // nvl=净值数据, ai=业绩走势对比
  months?: number; // 数据月数: 1,3,6,12,36,60,124
  source?: string; // 数据源，ai类型时使用
}

// API错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: unknown;
}

// 基金基本信息
export interface FundBasicInfo {
  code: string;
  name: string;
  type?: string;
  manager?: string;
  establishDate?: string;
  scale?: string;
  description?: string;
}

// 缓存项结构
export interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

// API响应状态
export type ApiStatus = "idle" | "loading" | "success" | "error";

// 数据获取选项
export interface DataFetchOptions {
  useCache?: boolean;
  timeout?: number;
  retryCount?: number;
  retryDelay?: number;
}

// 解析后的指数数据点
export interface ParsedIndexDataPoint {
  timestamp: number;
  time: string;
  open: number;
  close: number;
  volume: number;
  high: number;
  low: number;
  amount: number;
  range: number;
  ratio: number;
  turnoverratio: string;
  preClose: number;
  ma5avgprice?: number;
  ma5volume?: number;
  ma10avgprice?: number;
  ma10volume?: number;
  ma20avgprice?: number;
  ma20volume?: number;
}

// 指数API请求参数
export interface IndexApiParameters {
  indexCode: string;
  ktype?: "day" | "week" | "month"; // K线类型
  count?: number; // 数据条数
  endTime?: string; // 结束时间
}

// 中证指数API响应结构
export interface CSIndexApiResponse {
  code: string;
  msg: string;
  data: CSIndexDataPoint[];
}

// 中证指数数据点
export interface CSIndexDataPoint {
  tradeDate: string; // 交易日期，格式：YYYYMMDD
  indexCode: string; // 指数代码
  indexNameCnAll: string; // 指数中文全称
  indexNameCn: string; // 指数中文简称
  indexNameEnAll: string; // 指数英文全称
  indexNameEn: string; // 指数英文简称
  open: number; // 开盘价
  high: number; // 最高价
  low: number; // 最低价
  close: number; // 收盘价
  change: number; // 涨跌点数
  changePct: number; // 涨跌幅(%)
  tradingVol: number; // 成交量(万手)
  tradingValue: number; // 成交额(亿元)
  consNumber: number; // 成分股数量
  peg?: number; // 市盈率
}

// 中证指数列表查询请求
export interface CSIndexListRequest {
  sorter: {
    sortField: string | null;
    sortOrder: string | null;
  };
  pager: {
    pageNum: number;
    pageSize: number;
  };
  searchInput?: string; // 搜索关键词
  indexFilter: {
    ifCustomized?: boolean | null;
    ifTracked?: boolean | null;
    ifWeightCapped?: boolean | null;
    indexCompliance?: string | null;
    hotSpot?: string | null;
    indexClassify?: string | null;
    currency?: string | null;
    region?: string | null;
    indexSeries?: string | null;
    undefined?: null;
  };
}

// 中证指数列表响应
export interface CSIndexListResponse {
  code: string;
  msg: string;
  data: {
    total: number;
    list: CSIndexListItem[];
  };
}

// 中证指数列表项
export interface CSIndexListItem {
  indexCode: string;
  indexNameCn: string;
  indexNameEn: string;
  indexNameCnAll: string;
  indexNameEnAll: string;
  publishDate: string;
  baseDate: string;
  basePoint: number;
}
