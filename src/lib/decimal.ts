// Decimal.js utility functions for precise financial calculations

import { Decimal } from "decimal.js";

// Configure Decimal.js for financial calculations
Decimal.set({
  precision: 20, // 20 decimal places for high precision
  rounding: Decimal.ROUND_HALF_UP, // Standard rounding for financial calculations
  toExpNeg: -9, // Use exponential notation for numbers smaller than 1e-9
  toExpPos: 21, // Use exponential notation for numbers larger than 1e21
  modulo: Decimal.ROUND_DOWN, // Modulo operation rounding
});

/**
 * Create a new Decimal instance from various input types
 */
export function decimal(value: string | number | Decimal): Decimal {
  if (value instanceof Decimal) {
    return value;
  }
  return new Decimal(value);
}

/**
 * Safely convert a value to Decimal, returning zero for invalid inputs
 */
export function safeDecimal(value: unknown): Decimal {
  try {
    if (value === null || value === undefined || value === "") {
      return new Decimal(0);
    }
    return new Decimal(value.toString());
  } catch {
    return new Decimal(0);
  }
}

/**
 * Convert Decimal to number for display purposes
 * Use with caution - only for final display, not for calculations
 */
export function toNumber(value: Decimal): number {
  return value.toNumber();
}

/**
 * Format Decimal as percentage string
 */
export function formatPercentage(value: Decimal, decimals = 2): string {
  return `${value.mul(100).toFixed(decimals)}%`;
}

/**
 * Format Decimal as currency string
 */
export function formatCurrency(
  value: Decimal,
  currency = "CNY",
  locale = "zh-CN"
): string {
  const numberValue = value.toNumber();
  return new Intl.NumberFormat(locale, {
    style: "currency",
    currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numberValue);
}


/**
 * Calculate mean of an array of Decimal values
 */
function calculateMean(values: Decimal[]): Decimal {
  if (values.length === 0) {
    return new Decimal(0);
  }

  let sum = new Decimal(0);
  for (const value of values) {
    sum = sum.add(value);
  }
  return sum.div(values.length);
}

/**
 * Calculate variance of an array of Decimal values
 */
function calculateVariance(values: Decimal[]): Decimal {
  if (values.length < 2) {
    return new Decimal(0);
  }

  const mean = calculateMean(values);
  const squaredDifferences = values.map((value) => value.sub(mean).pow(2));
  return calculateMean(squaredDifferences);
}

/**
 * Calculate standard deviation of an array of Decimal values
 */
function calculateStandardDeviation(values: Decimal[]): Decimal {
  const variance = calculateVariance(values);
  return variance.sqrt();
}

/**
 * Calculate volatility (annualized standard deviation)
 */
export function calculateVolatility(
  returns: Decimal[],
  periodsPerYear = 252
): Decimal {
  if (returns.length < 2) {
    return new Decimal(0);
  }

  const standardDeviation = calculateStandardDeviation(returns);
  return standardDeviation.mul(new Decimal(periodsPerYear).sqrt());
}

/**
 * Calculate maximum drawdown from a series of values
 */
export function calculateMaxDrawdown(values: Decimal[]): Decimal {
  if (values.length < 2) {
    return new Decimal(0);
  }

  let maxDrawdown = new Decimal(0);
  let peak = values[0];

  for (const value of values) {
    if (value.gt(peak)) {
      peak = value;
    }

    const drawdown = peak.sub(value).div(peak);
    if (drawdown.gt(maxDrawdown)) {
      maxDrawdown = drawdown;
    }
  }

  return maxDrawdown;
}

/**
 * Calculate Sharpe ratio
 */
export function calculateSharpeRatio(
  annualizedReturn: Decimal,
  volatility: Decimal,
  riskFreeRate: Decimal = new Decimal(0.03)
): Decimal {
  if (volatility.isZero()) {
    return new Decimal(0);
  }

  return annualizedReturn.sub(riskFreeRate).div(volatility);
}


