"use client";

import { useState } from "react";

import type { Fund } from "@/types/fund";

import FundSearch from "./fund-search";

const getFundTypeLabel = (type: Fund["type"]) => {
  const typeLabels = {
    stock: "股票型",
    bond: "债券型",
    hybrid: "混合型",
    index: "指数型",
    money: "货币型",
  };
  return typeLabels[type];
};

const getFundTypeColor = (type: Fund["type"]) => {
  const typeColors = {
    stock: "bg-red-100 text-red-800",
    bond: "bg-green-100 text-green-800",
    hybrid: "bg-blue-100 text-blue-800",
    index: "bg-purple-100 text-purple-800",
    money: "bg-yellow-100 text-yellow-800",
  };
  return typeColors[type];
};

interface FundSelectorProperties {
  selectedFund: Fund | null;
  onFundSelect: (fund: Fund) => void;
  funds: Fund[];
}

export default function FundSelector({
  selectedFund,
  onFundSelect,
  funds,
}: FundSelectorProperties) {
  const [searchTerm, setSearchTerm] = useState("");
  const [useAdvancedSearch, setUseAdvancedSearch] = useState(false);

  const filteredFunds = funds.filter(
    (fund) =>
      fund.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fund.code.includes(searchTerm)
  );

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">选择基金</h3>
        <button
          onClick={() => {
            setUseAdvancedSearch(!useAdvancedSearch);
          }}
          className="text-sm text-blue-600 hover:text-blue-800 font-medium"
        >
          {useAdvancedSearch ? "简单搜索" : "高级搜索"}
        </button>
      </div>

      {/* 搜索组件 */}
      {useAdvancedSearch ? (
        <FundSearch
          onFundSelect={onFundSelect}
          selectedFund={selectedFund || undefined}
          placeholder="输入基金代码或名称进行搜索..."
          className="w-full"
        />
      ) : (
        <div className="relative">
          <input
            type="text"
            placeholder="搜索基金名称或代码..."
            value={searchTerm}
            onChange={(event) => {
              setSearchTerm(event.target.value);
            }}
            className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute inset-y-0 right-0 flex items-center pr-3">
            <svg
              className="h-5 w-5 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
          </div>
        </div>
      )}

      {/* 基金列表 - 只在简单搜索模式下显示 */}
      {!useAdvancedSearch && (
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {filteredFunds.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm ? "未找到匹配的基金" : "暂无基金数据"}
            </div>
          ) : (
            filteredFunds.map((fund) => (
              <div
                key={fund.id}
                onClick={() => {
                  onFundSelect(fund);
                }}
                className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:shadow-md ${
                  selectedFund?.id === fund.id
                    ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200"
                    : "border-gray-200 hover:border-gray-300"
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-medium text-gray-900">{fund.name}</h4>
                      <span
                        className={`px-2 py-1 text-xs font-medium rounded-full ${getFundTypeColor(fund.type)}`}
                      >
                        {getFundTypeLabel(fund.type)}
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 mb-1">
                      代码: {fund.code}
                    </p>
                    {fund.description ? (
                      <p className="text-sm text-gray-500">
                        {fund.description}
                      </p>
                    ) : null}
                  </div>

                  {selectedFund?.id === fund.id && (
                    <div className="ml-3 flex-shrink-0">
                      <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg
                          className="w-3 h-3 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      )}

      {/* 选中的基金信息 */}
      {selectedFund ? (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">已选择基金</h4>
          <div className="text-sm text-blue-800">
            <p>
              <span className="font-medium">名称:</span> {selectedFund.name}
            </p>
            <p>
              <span className="font-medium">代码:</span> {selectedFund.code}
            </p>
            <p>
              <span className="font-medium">类型:</span>{" "}
              {getFundTypeLabel(selectedFund.type)}
            </p>
            {selectedFund.description ? (
              <p>
                <span className="font-medium">描述:</span>{" "}
                {selectedFund.description}
              </p>
            ) : null}
          </div>
        </div>
      ) : null}
    </div>
  );
}
