import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";

import FundSelector from "@/components/FundSelector";
import type { Fund } from "@/types/fund";

// Mock FundSearch component
vi.mock("@/components/FundSearch", () => ({
  default: ({ onFundSelect, selectedFund, placeholder }: any) => (
    <div data-testid="fund-search">
      <input
        placeholder={placeholder}
        data-testid="advanced-search-input"
        onChange={(event) => {
          if (event.target.value === "test") {
            onFundSelect(mockFunds[0]);
          }
        }}
      />
      {selectedFund ? (
        <div data-testid="selected-fund">{selectedFund.name}</div>
      ) : null}
    </div>
  ),
}));

const mockFunds: Fund[] = [
  {
    id: "fund_001",
    name: "测试股票基金",
    code: "000001",
    type: "stock",
    description: "这是一个测试股票基金",
  },
  {
    id: "fund_002",
    name: "测试债券基金",
    code: "000002",
    type: "bond",
    description: "这是一个测试债券基金",
  },
  {
    id: "fund_003",
    name: "测试混合基金",
    code: "000003",
    type: "hybrid",
  },
];

describe("FundSelector", () => {
  const mockOnFundSelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该渲染基金列表", () => {
    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    expect(screen.getByText("选择基金")).toBeInTheDocument();
    expect(screen.getByText("测试股票基金")).toBeInTheDocument();
  });

  it("应该渲染基金列表", async () => {
    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("测试股票基金")).toBeInTheDocument();
      expect(screen.getByText("测试债券基金")).toBeInTheDocument();
      expect(screen.getByText("测试混合基金")).toBeInTheDocument();
    });
  });

  it("应该显示基金类型标签", async () => {
    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("股票型")).toBeInTheDocument();
      expect(screen.getByText("债券型")).toBeInTheDocument();
      expect(screen.getByText("混合型")).toBeInTheDocument();
    });
  });

  it("应该支持基金搜索", async () => {
    const user = userEvent.setup();

    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("测试股票基金")).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText("搜索基金名称或代码...");
    await user.type(searchInput, "股票");

    expect(screen.getByText("测试股票基金")).toBeInTheDocument();
    expect(screen.queryByText("测试债券基金")).not.toBeInTheDocument();
  });

  it("应该支持按代码搜索", async () => {
    const user = userEvent.setup();

    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("测试股票基金")).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText("搜索基金名称或代码...");
    await user.type(searchInput, "000001");

    expect(screen.getByText("测试股票基金")).toBeInTheDocument();
    expect(screen.queryByText("测试债券基金")).not.toBeInTheDocument();
  });

  it("应该处理基金选择", async () => {
    const user = userEvent.setup();

    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("测试股票基金")).toBeInTheDocument();
    });

    await user.click(screen.getByText("测试股票基金"));

    expect(mockOnFundSelect).toHaveBeenCalledWith(mockFunds[0]);
  });

  it("应该显示选中的基金", async () => {
    render(
      <FundSelector
        selectedFund={mockFunds[0]}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("已选择基金")).toBeInTheDocument();
      expect(screen.getAllByText("测试股票基金")).toHaveLength(2); // One in list, one in selected section
      expect(screen.getByText("000001")).toBeInTheDocument();
    });
  });

  it("应该高亮选中的基金项", async () => {
    render(
      <FundSelector
        selectedFund={mockFunds[0]}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      // Find the fund item in the list (not in the selected section)
      const fundItems = screen.getAllByText("测试股票基金");
      const fundListItem = fundItems[0].closest(".p-4"); // The first one should be in the list
      expect(fundListItem).toHaveClass("border-blue-500", "bg-blue-50");
    });
  });

  it("应该切换到高级搜索模式", async () => {
    const user = userEvent.setup();

    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("高级搜索")).toBeInTheDocument();
    });

    await user.click(screen.getByText("高级搜索"));

    expect(screen.getByTestId("fund-search")).toBeInTheDocument();
    expect(screen.getByText("简单搜索")).toBeInTheDocument();
  });

  it("应该在高级搜索模式下隐藏基金列表", async () => {
    const user = userEvent.setup();

    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("测试股票基金")).toBeInTheDocument();
    });

    await user.click(screen.getByText("高级搜索"));

    expect(screen.queryByText("测试股票基金")).not.toBeInTheDocument();
  });

  it("应该处理空搜索结果", async () => {
    const user = userEvent.setup();

    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("测试股票基金")).toBeInTheDocument();
    });

    const searchInput = screen.getByPlaceholderText("搜索基金名称或代码...");
    await user.type(searchInput, "不存在的基金");

    expect(screen.getByText("未找到匹配的基金")).toBeInTheDocument();
  });

  it("应该处理空基金列表", async () => {
    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={[]}
      />
    );

    expect(screen.getByText("暂无基金数据")).toBeInTheDocument();
  });

  it("应该显示基金描述", async () => {
    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("这是一个测试股票基金")).toBeInTheDocument();
      expect(screen.getByText("这是一个测试债券基金")).toBeInTheDocument();
    });
  });

  it("应该处理没有描述的基金", async () => {
    render(
      <FundSelector
        selectedFund={null}
        onFundSelect={mockOnFundSelect}
        funds={mockFunds}
      />
    );

    await waitFor(() => {
      expect(screen.getByText("测试混合基金")).toBeInTheDocument();
    });

    // 混合基金没有描述，不应该显示描述文本
    const fundElement = screen.getByText("测试混合基金").closest("div");
    expect(fundElement).not.toHaveTextContent("这是一个");
  });

  describe("基金类型样式", () => {
    it("应该为不同基金类型应用正确的样式", async () => {
      render(
        <FundSelector
          selectedFund={null}
          onFundSelect={mockOnFundSelect}
          funds={mockFunds}
        />
      );

      await waitFor(() => {
        const stockType = screen.getByText("股票型");
        const bondType = screen.getByText("债券型");
        const hybridType = screen.getByText("混合型");

        expect(stockType).toHaveClass("bg-red-100", "text-red-800");
        expect(bondType).toHaveClass("bg-green-100", "text-green-800");
        expect(hybridType).toHaveClass("bg-blue-100", "text-blue-800");
      });
    });
  });
});
