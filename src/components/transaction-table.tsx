"use client";

import { format } from "date-fns";
import { zhCN } from "date-fns/locale";

import { decimal, toNumber } from "@/lib/decimal";
import type { BacktestResult } from "@/types/fund";

interface TransactionTableProperties {
  result: BacktestResult;
  className?: string;
}

interface Transaction {
  date: string;
  type: "buy" | "sell";
  amount: number;
  shares: number;
  price: number;
  totalShares: number;
  totalValue: number;
  return: number;
}

export default function TransactionTable({
  result,
  className = "",
}: TransactionTableProperties) {
  // 从回测结果中提取交易记录
  const transactions: Transaction[] = result.timeline
    .filter((item) => item.investment !== 0) // 只显示有投资的记录
    .map((item, index) => {
      const isFirstTransaction = index === 0;
      const type = item.investment > 0 ? "buy" : "sell";
      
      return {
        date: item.date,
        type,
        amount: Math.abs(item.investment),
        shares: isFirstTransaction 
          ? item.shares 
          : item.shares - (result.timeline[index - 1]?.shares || 0),
        price: item.netAssetValue,
        totalShares: item.shares,
        totalValue: item.value,
        return: item.return,
      };
    });

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("zh-CN", {
      style: "currency",
      currency: "CNY",
      minimumFractionDigits: 2,
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? "+" : ""}${value.toFixed(2)}%`;
  };

  const formatShares = (value: number) => {
    return value.toFixed(4);
  };

  const getTypeLabel = (type: "buy" | "sell") => {
    return type === "buy" ? "买入" : "卖出";
  };

  const getTypeColor = (type: "buy" | "sell") => {
    return type === "buy" 
      ? "bg-green-100 text-green-800" 
      : "bg-red-100 text-red-800";
  };

  const getReturnColor = (returnValue: number) => {
    if (returnValue > 0) return "text-green-600";
    if (returnValue < 0) return "text-red-600";
    return "text-gray-600";
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">交易记录</h3>
        <p className="text-sm text-gray-500 mt-1">
          共 {transactions.length} 笔交易
        </p>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                日期
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                金额
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                份额
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                净值
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                累计份额
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                总价值
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                收益率
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {transactions.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-6 py-12 text-center text-gray-500">
                  暂无交易记录
                </td>
              </tr>
            ) : (
              transactions.map((transaction, index) => (
                <tr key={`${transaction.date}-${index}`} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {format(new Date(transaction.date), "yyyy-MM-dd", {
                      locale: zhCN,
                    })}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getTypeColor(transaction.type)}`}
                    >
                      {getTypeLabel(transaction.type)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {formatCurrency(transaction.amount)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {formatShares(transaction.shares)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {formatCurrency(transaction.price)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {formatShares(transaction.totalShares)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-right">
                    {formatCurrency(transaction.totalValue)}
                  </td>
                  <td className={`px-6 py-4 whitespace-nowrap text-sm text-right font-medium ${getReturnColor(transaction.return)}`}>
                    {formatPercent(transaction.return)}
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* 汇总信息 */}
      {transactions.length > 0 && (
        <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-500">总投入:</span>
              <span className="ml-2 font-medium text-gray-900">
                {formatCurrency(result.performance.totalInvestment)}
              </span>
            </div>
            <div>
              <span className="text-gray-500">最终价值:</span>
              <span className="ml-2 font-medium text-gray-900">
                {formatCurrency(result.performance.finalValue)}
              </span>
            </div>
            <div>
              <span className="text-gray-500">总收益:</span>
              <span className={`ml-2 font-medium ${getReturnColor(result.performance.totalReturn)}`}>
                {formatCurrency(result.performance.finalValue - result.performance.totalInvestment)}
              </span>
            </div>
            <div>
              <span className="text-gray-500">总收益率:</span>
              <span className={`ml-2 font-medium ${getReturnColor(result.performance.totalReturn)}`}>
                {formatPercent(result.performance.totalReturn)}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
