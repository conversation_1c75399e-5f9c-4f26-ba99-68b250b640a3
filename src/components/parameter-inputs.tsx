"use client";

import { useState, useEffect } from "react";

import type { Strategy } from "@/types/fund";

interface ParameterInputsProperties {
  strategy: Strategy;
  parameters: Record<string, unknown>;
  onParametersChange: (parameters: Record<string, unknown>) => void;
}

export default function ParameterInputs({
  strategy,
  parameters,
  onParametersChange,
}: ParameterInputsProperties) {
  const [localParameters, setLocalParameters] = useState<Record<string, unknown>>(
    {}
  );
  const [errors, setErrors] = useState<Record<string, string>>({});

  // 初始化参数
  useEffect(() => {
    const initialParameters: Record<string, unknown> = {};
    for (const [key, parameter] of Object.entries(strategy.parameterSchema)) {
      initialParameters[key] = parameters[key] ?? parameter.defaultValue ?? "";
    }
    setLocalParameters(initialParameters);
    onParametersChange(initialParameters);
  }, [strategy.id, onParametersChange, parameters, strategy.parameterSchema]);

  // 验证参数
  const validateParameter = (key: string, value: unknown): string => {
    const parameter = strategy.parameterSchema[key];

    if (parameter.required && (!value || value === "")) {
      return `${parameter.label}是必填项`;
    }

    if (parameter.type === "number" && value !== "") {
      const numberValue = Number(value);
      if (Number.isNaN(numberValue)) {
        return `${parameter.label}必须是数字`;
      }
      if (parameter.min !== undefined && numberValue < parameter.min) {
        return `${parameter.label}不能小于${parameter.min}`;
      }
      if (parameter.max !== undefined && numberValue > parameter.max) {
        return `${parameter.label}不能大于${parameter.max}`;
      }
    }

    return "";
  };

  // 处理参数变化
  const handleParameterChange = (key: string, value: unknown) => {
    const newParameters = { ...localParameters, [key]: value };
    setLocalParameters(newParameters);

    // 验证当前参数
    const error = validateParameter(key, value);
    setErrors((previous) => ({ ...previous, [key]: error }));

    // 如果没有错误，更新父组件
    if (!error) {
      onParametersChange(newParameters);
    }
  };

  // 渲染不同类型的输入组件
  const renderInput = (
    key: string,
    parameter: Strategy["parameterSchema"][string]
  ) => {
    const value = localParameters[key] ?? "";
    const error = errors[key];

    const baseInputClasses = `w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
      error ? "border-red-300" : "border-gray-300"
    }`;

    switch (parameter.type) {
      case "number": {
        return (
          <input
            type="number"
            value={value}
            onChange={(event) => {
              handleParameterChange(key, event.target.value);
            }}
            min={parameter.min}
            max={parameter.max}
            step={parameter.step}
            className={baseInputClasses}
            placeholder={`请输入${parameter.label}`}
          />
        );
      }

      case "date": {
        return (
          <input
            type="date"
            value={value}
            onChange={(event) => {
              handleParameterChange(key, event.target.value);
            }}
            className={baseInputClasses}
          />
        );
      }

      case "select": {
        return (
          <select
            value={value}
            onChange={(event) => {
              handleParameterChange(key, event.target.value);
            }}
            className={baseInputClasses}
          >
            {parameter.options?.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );
      }

      case "range": {
        return (
          <div className="space-y-2">
            <input
              type="range"
              value={value}
              onChange={(event) => {
                handleParameterChange(key, event.target.value);
              }}
              min={parameter.min}
              max={parameter.max}
              step={parameter.step}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-sm text-gray-500">
              <span>{parameter.min}</span>
              <span className="font-medium">{value}</span>
              <span>{parameter.max}</span>
            </div>
          </div>
        );
      }

      default: {
        return (
          <input
            type="text"
            value={value}
            onChange={(event) => {
              handleParameterChange(key, event.target.value);
            }}
            className={baseInputClasses}
            placeholder={`请输入${parameter.label}`}
          />
        );
      }
    }
  };

  return (
    <div className="space-y-4">
      {Object.entries(strategy.parameterSchema).map(([key, parameter]) => (
        <div key={key} className="space-y-1">
          <label className="block text-sm font-medium text-gray-700">
            {parameter.label}
            {parameter.required && (
              <span className="text-red-500 ml-1">*</span>
            )}
          </label>
          {renderInput(key, parameter)}
          {errors[key] && (
            <p className="text-sm text-red-500 mt-1">{errors[key]}</p>
          )}
          {parameter.description && (
            <p className="text-xs text-gray-500 mt-1">{parameter.description}</p>
          )}
        </div>
      ))}
    </div>
  );
}

// 这些函数已不再使用，但保留为参考
