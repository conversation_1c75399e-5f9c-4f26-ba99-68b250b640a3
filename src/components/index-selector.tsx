"use client";

import { useState, useTransition } from "react";

interface IndexType {
  id: string;
  name: string;
  code: string;
  description?: string;
  type?: string;
  classify?: string;
  currency?: string;
  region?: string;
  publishDate?: string;
  baseDate?: string;
  basePoint?: number;
}

interface IndexSelectorProperties {
  selectedIndex: IndexType | null;
  onIndexSelect: (index: IndexType) => void;
  className?: string;
}

export default function IndexSelector({
  selectedIndex,
  onIndexSelect,
  className = "",
}: IndexSelectorProperties) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<IndexType[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [, startTransition] = useTransition();

  // 搜索指数
  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    startTransition(async () => {
      try {
        const response = await fetch(
          `/api/index/search?q=${encodeURIComponent(searchQuery)}`
        );
        const result = await response.json();

        if (result.success) {
          setResults(result.data);
        } else {
          console.error("搜索指数失败:", result.error);
          setResults([]);
        }
      } catch (error) {
        console.error("搜索指数失败:", error);
        setResults([]);
      } finally {
        setIsLoading(false);
      }
    });
  };

  // 选择指数
  const handleIndexSelect = (index: IndexType) => {
    setQuery(index.name);
    setIsOpen(false);
    onIndexSelect(index);
  };

  // 输入变化处理
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setQuery(value);
    setIsOpen(true);

    // 使用防抖搜索
    const timer = setTimeout(() => {
      handleSearch(value);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  };

  // 渲染空状态
  const renderEmptyState = () => {
    if (!query.trim()) {
      return (
        <div className="px-3 py-2 text-gray-500 text-center">
          请输入指数名称或代码进行搜索
        </div>
      );
    }

    return (
      <div className="px-3 py-2 text-gray-500 text-center">
        未找到匹配的指数
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">选择指数</h3>
        <span className="text-sm text-gray-500">用于对比分析</span>
      </div>

      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => {
            setIsOpen(true);
          }}
          placeholder="搜索指数名称或代码..."
          className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />

        {/* 搜索图标 */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          <svg
            className="h-5 w-5 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
            />
          </svg>
        </div>
      </div>

      {/* 搜索结果下拉列表 */}
      {isOpen && (query.trim() || results.length > 0) ? (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {(() => {
            if (isLoading) {
              return (
                <div className="px-3 py-2 text-gray-500 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent" />
                    <span>搜索中...</span>
                  </div>
                </div>
              );
            } else if (results.length > 0) {
              return results.map((index) => (
                <div
                  key={index.id}
                  onClick={() => {
                    handleIndexSelect(index);
                  }}
                  className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex justify-between items-center">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{index.name}</div>
                      <div className="text-sm text-gray-500">
                        代码: {index.code}
                        {index.classify && ` | 分类: ${index.classify}`}
                      </div>
                      {index.description && (
                        <div className="text-xs text-gray-400 mt-1">
                          {index.description}
                        </div>
                      )}
                    </div>
                    {selectedIndex?.id === index.id && (
                      <svg
                        className="h-4 w-4 text-blue-500 flex-shrink-0 ml-2"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                    )}
                  </div>
                </div>
              ));
            } else {
              return renderEmptyState();
            }
          })()}
        </div>
      ) : null}

      {/* 点击外部关闭下拉列表 */}
      {isOpen ? (
        <div
          className="fixed inset-0 z-0"
          onClick={() => {
            setIsOpen(false);
          }}
        />
      ) : null}

      {/* 选中的指数信息 */}
      {selectedIndex ? (
        <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
          <h4 className="font-medium text-green-900 mb-2">已选择指数</h4>
          <div className="text-sm text-green-800">
            <p>
              <span className="font-medium">名称:</span> {selectedIndex.name}
            </p>
            <p>
              <span className="font-medium">代码:</span> {selectedIndex.code}
            </p>
            {selectedIndex.classify && (
              <p>
                <span className="font-medium">分类:</span> {selectedIndex.classify}
              </p>
            )}
            {selectedIndex.description && (
              <p>
                <span className="font-medium">描述:</span> {selectedIndex.description}
              </p>
            )}
          </div>
        </div>
      ) : null}
    </div>
  );
}
