import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { describe, it, expect, vi, beforeEach } from "vitest";

import type { Strategy } from "@/types/fund";

import ParameterInputs from "./ParameterInputs";

const mockStrategy: Strategy = {
  id: "fixed_amount",
  name: "定期定额投资",
  description: "定期定额投资策略",
  riskLevel: "low",
  complexity: "beginner",
  parameterSchema: {
    startDate: {
      type: "date",
      label: "开始日期",
      required: true,
      defaultValue: "2024-01-01",
    },
    endDate: {
      type: "date",
      label: "结束日期",
      required: true,
      defaultValue: "2024-12-31",
    },
    initialAmount: {
      type: "number",
      label: "初始投资金额",
      required: true,
      min: 100,
      max: 1_000_000,
      defaultValue: 10_000,
    },
    monthlyAmount: {
      type: "number",
      label: "每月投资金额",
      required: true,
      min: 100,
      max: 100_000,
      defaultValue: 1000,
    },
    frequency: {
      type: "select",
      label: "投资频率",
      required: true,
      options: [
        { value: "weekly", label: "每周" },
        { value: "monthly", label: "每月" },
        { value: "quarterly", label: "每季度" },
      ],
      defaultValue: "monthly",
    },
  },
};

const mockParameters = {
  startDate: "2024-01-01",
  endDate: "2024-12-31",
  initialAmount: 10_000,
  monthlyAmount: 1000,
  frequency: "monthly",
};

describe("ParameterInputs", () => {
  const mockOnParametersChange = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it("应该渲染所有基本输入字段", () => {
    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    expect(screen.getByText("策略参数设置")).toBeInTheDocument();
    expect(screen.getByLabelText("开始日期")).toBeInTheDocument();
    expect(screen.getByLabelText("结束日期")).toBeInTheDocument();
    expect(screen.getByLabelText("初始投资金额")).toBeInTheDocument();
    expect(screen.getByLabelText("每月投资金额")).toBeInTheDocument();
    expect(screen.getByLabelText("投资频率")).toBeInTheDocument();
  });

  it("应该显示正确的初始值", () => {
    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    expect(screen.getByDisplayValue("2024-01-01")).toBeInTheDocument();
    expect(screen.getByDisplayValue("2024-12-31")).toBeInTheDocument();
    expect(screen.getByDisplayValue("10000")).toBeInTheDocument();
    expect(screen.getByDisplayValue("1000")).toBeInTheDocument();
    expect(screen.getByDisplayValue("monthly")).toBeInTheDocument();
  });

  it("应该在输入变化时调用回调函数", async () => {
    const user = userEvent.setup();

    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    const startDateInput = screen.getByLabelText("开始日期");
    await user.clear(startDateInput);
    await user.type(startDateInput, "2023-01-01");

    await waitFor(() => {
      expect(mockOnParametersChange).toHaveBeenCalledWith(
        expect.objectContaining({
          startDate: "2023-01-01",
        })
      );
    });
  });

  it("应该验证数字输入", async () => {
    const user = userEvent.setup();

    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    const amountInput = screen.getByLabelText("初始投资金额");
    await user.clear(amountInput);
    await user.type(amountInput, "abc"); // 非数字

    await waitFor(() => {
      expect(screen.getByText("初始投资金额必须是数字")).toBeInTheDocument();
    });
  });

  it("应该验证数字范围", async () => {
    const user = userEvent.setup();

    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    const amountInput = screen.getByLabelText("初始投资金额");
    await user.clear(amountInput);
    await user.type(amountInput, "50"); // 小于最小值

    await waitFor(() => {
      expect(screen.getByText("初始投资金额不能小于100")).toBeInTheDocument();
    });
  });

  it("应该处理选择框输入", async () => {
    const user = userEvent.setup();

    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    const frequencySelect = screen.getByLabelText("投资频率");
    await user.selectOptions(frequencySelect, "weekly");

    await waitFor(() => {
      expect(mockOnParametersChange).toHaveBeenCalledWith(
        expect.objectContaining({
          frequency: "weekly",
        })
      );
    });
  });

  it("应该显示参数预览", () => {
    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    expect(screen.getByText("参数预览")).toBeInTheDocument();
    expect(screen.getByText("开始日期:")).toBeInTheDocument();
    expect(screen.getByText("结束日期:")).toBeInTheDocument();
    expect(screen.getByText("初始投资金额:")).toBeInTheDocument();
  });

  it("应该显示必填字段标记", () => {
    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    // 检查必填字段的星号标记
    const requiredLabels = screen.getAllByText("*");
    expect(requiredLabels.length).toBeGreaterThan(0);
  });

  it("应该处理空值输入", async () => {
    const user = userEvent.setup();

    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    const amountInput = screen.getByLabelText("初始投资金额");
    await user.clear(amountInput);

    await waitFor(() => {
      expect(screen.getByText("初始投资金额是必填项")).toBeInTheDocument();
    });
  });

  it("应该显示验证错误状态", async () => {
    const user = userEvent.setup();

    render(
      <ParameterInputs
        strategy={mockStrategy}
        parameters={mockParameters}
        onParametersChange={mockOnParametersChange}
      />
    );

    const amountInput = screen.getByLabelText("初始投资金额");
    await user.clear(amountInput);
    await user.type(amountInput, "invalid");

    await waitFor(() => {
      expect(
        screen.getByText("请修正参数错误后再进行回测")
      ).toBeInTheDocument();
    });
  });
});
