"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  ReferenceLine,
} from "recharts";

import type { BacktestResult, StrategyType } from "@/types/fund";

interface PerformanceComparisonChartProperties {
  result: BacktestResult;
}

// 格式化百分比
const formatPercent = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

// 格式化日期 - 使用 date-fns
const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch {
    return dateString;
  }
};

// 获取策略名称
const getStrategyDisplayName = (strategyType: StrategyType): string => {
  switch (strategyType) {
    case "fixed_amount": {
      return "定投策略";
    }
    case "value_averaging": {
      return "价值平均";
    }
    case "smart_fixed": {
      return "智能定投";
    }
    case "grid_trading": {
      return "网格交易";
    }
    case "momentum": {
      return "动量策略";
    }
    case "mean_reversion": {
      return "均值回归";
    }
    default: {
      return "投资策略";
    }
  }
};

interface TooltipProperties {
  active?: boolean;
  payload?: Array<{
    value: number;
    name: string;
    color: string;
  }>;
  label?: string;
}

interface ChartEntry {
  value: number;
  name: string;
  color: string;
}

// 自定义Tooltip组件
const CustomTooltip = ({ active, payload, label }: TooltipProperties) => {
  if (active && payload?.length) {
    return (
      <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
        <p className="text-sm text-gray-600 mb-2">{formatDate(label || "")}</p>
        {payload.map((entry: ChartEntry, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            <span className="font-medium">{entry.name}:</span>{" "}
            {formatPercent(entry.value)}
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// 渲染收益率卡片
const RenderReturnCard = ({
  type,
  value,
  label,
  color,
}: {
  type: string;
  value: number;
  label: string;
  color: string;
}) => (
  <div className={`bg-${color}-50 p-4 rounded-lg`}>
    <div className={`text-sm text-${color}-600 font-medium`}>{type}</div>
    <div className={`text-2xl font-bold text-${color}-900`}>
      {formatPercent(value)}
    </div>
    <div className={`text-xs text-${color}-700 mt-1`}>{label}</div>
  </div>
);

// 渲染超额收益卡片
const RenderExcessReturnCard = ({
  type,
  value,
}: {
  type: string;
  value: number;
}) => {
  const isPositive = value >= 0;
  const colorClass = isPositive ? "green" : "red";
  
  return (
    <div className={`p-4 rounded-lg bg-${colorClass}-50`}>
      <div className={`text-sm font-medium text-${colorClass}-600`}>{type}</div>
      <div className={`text-xl font-bold text-${colorClass}-900`}>
        {isPositive ? "+" : ""}
        {formatPercent(value)}
      </div>
    </div>
  );
};

export default function PerformanceComparisonChart({
  result,
}: PerformanceComparisonChartProperties) {
  // 如果没有对比数据，则计算生成
  const comparisonData =
    result.performanceComparison || generateComparisonData(result);

  // 计算最终收益率
  const finalData = comparisonData.at(-1);
  const strategyFinalReturn = finalData?.strategyReturn || 0;
  const fundFinalReturn = finalData?.fundReturn || 0;
  const indexFinalReturn = finalData?.indexReturn || 0;

  // 计算超额收益
  const excessReturnVsFund = strategyFinalReturn - fundFinalReturn;
  const excessReturnVsIndex = indexFinalReturn
    ? strategyFinalReturn - indexFinalReturn
    : null;

  return (
    <div className="space-y-6">
      <div className="bg-white p-6 border border-gray-200 rounded-lg">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">
          收益率对比分析
        </h4>

        {/* 最终收益率对比 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-blue-50 p-4 rounded-lg">
            <div className="text-sm text-blue-600 font-medium">策略收益率</div>
            <div className="text-2xl font-bold text-blue-900">
              {formatPercent(strategyFinalReturn)}
            </div>
            <div className="text-xs text-blue-700 mt-1">
              {getStrategyDisplayName(result.strategy)}
            </div>
          </div>

          <div className="bg-green-50 p-4 rounded-lg">
            <div className="text-sm text-green-600 font-medium">基金收益率</div>
            <div className="text-2xl font-bold text-green-900">
              {formatPercent(fundFinalReturn)}
            </div>
            <div className="text-xs text-green-700 mt-1">
              {result.fund.name}
            </div>
          </div>

          {indexFinalReturn !== 0 && (
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="text-sm text-purple-600 font-medium">
                指数收益率
              </div>
              <div className="text-2xl font-bold text-purple-900">
                {formatPercent(indexFinalReturn)}
              </div>
              <div className="text-xs text-purple-700 mt-1">基准指数</div>
            </div>
          )}
        </div>

        {/* 超额收益 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div
            className={`p-4 rounded-lg ${excessReturnVsFund >= 0 ? "bg-green-50" : "bg-red-50"}`}
          >
            <div
              className={`text-sm font-medium ${excessReturnVsFund >= 0 ? "text-green-600" : "text-red-600"}`}
            >
              相对基金超额收益
            </div>
            <div
              className={`text-xl font-bold ${excessReturnVsFund >= 0 ? "text-green-900" : "text-red-900"}`}
            >
              {excessReturnVsFund >= 0 ? "+" : ""}
              {formatPercent(excessReturnVsFund)}
            </div>
          </div>

          {excessReturnVsIndex !== null && (
            <div
              className={`p-4 rounded-lg ${excessReturnVsIndex >= 0 ? "bg-green-50" : "bg-red-50"}`}
            >
              <div
                className={`text-sm font-medium ${excessReturnVsIndex >= 0 ? "text-green-600" : "text-red-600"}`}
              >
                相对指数超额收益
              </div>
              <div
                className={`text-xl font-bold ${excessReturnVsIndex >= 0 ? "text-green-900" : "text-red-900"}`}
              >
                {excessReturnVsIndex >= 0 ? "+" : ""}
                {formatPercent(excessReturnVsIndex)}
              </div>
            </div>
          )}
        </div>

        {/* 收益率走势图 */}
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={comparisonData}
              margin={{
                top: 20,
                right: 30,
                left: 20,
                bottom: 20,
              }}
            >
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis
                dataKey="date"
                tickFormatter={formatDate}
                stroke="#666"
                fontSize={12}
                interval="preserveStartEnd"
              />
              <YAxis
                tickFormatter={formatPercent}
                stroke="#666"
                fontSize={12}
              />
              <Tooltip content={<CustomTooltip />} />
              <Legend />

              {/* 零轴参考线 */}
              <ReferenceLine y={0} stroke="#999" strokeDasharray="2 2" />

              {/* 策略收益率线 */}
              <Line
                type="monotone"
                dataKey="strategyReturn"
                stroke="#3b82f6"
                strokeWidth={3}
                name="策略收益率"
                dot={false}
                activeDot={{ r: 6, fill: "#3b82f6" }}
              />

              {/* 基金收益率线 */}
              <Line
                type="monotone"
                dataKey="fundReturn"
                stroke="#10b981"
                strokeWidth={2}
                name="基金收益率"
                dot={false}
                activeDot={{ r: 4, fill: "#10b981" }}
              />

              {/* 指数收益率线 */}
              {indexFinalReturn !== 0 && (
                <Line
                  type="monotone"
                  dataKey="indexReturn"
                  stroke="#8b5cf6"
                  strokeWidth={2}
                  name="指数收益率"
                  dot={false}
                  activeDot={{ r: 4, fill: "#8b5cf6" }}
                  strokeDasharray="5 5"
                />
              )}
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* 图表说明 */}
        <div className="mt-4 text-sm text-gray-600">
          <p className="mb-2">
            <strong>图表说明：</strong>
          </p>
          <ul className="list-disc list-inside space-y-1">
            <li>
              <span className="text-blue-600 font-medium">策略收益率</span>
              ：使用投资策略的累计收益率
            </li>
            <li>
              <span className="text-green-600 font-medium">基金收益率</span>
              ：基金净值的累计收益率（买入并持有）
            </li>
            {indexFinalReturn !== 0 && (
              <li>
                <span className="text-purple-600 font-medium">指数收益率</span>
                ：基准指数的累计收益率
              </li>
            )}
            <li>超额收益为正值表示策略表现优于基准</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

/**
 * 生成收益率对比数据
 */
function generateComparisonData(result: BacktestResult) {
  const { timeline } = result;

  if (timeline.length === 0) {
    return [];
  }

  // 获取起始净值
  const startNav = timeline[0].netAssetValue;
  
  return timeline.map((entry) => {
    // 策略收益率 = (当前价值 - 累计投入) / 累计投入 * 100
    const strategyReturn =
      entry.totalInvestment > 0
        ? ((entry.value - entry.totalInvestment) / entry.totalInvestment) * 100
        : 0;

    // 基金收益率 = (当前净值 - 起始净值) / 起始净值 * 100
    const fundReturn = ((entry.netAssetValue - startNav) / startNav) * 100;

    // 指数收益率 - 从关联指数数据计算，如果有的话
    // 这里需要实现从指数数据计算收益率的逻辑
    const indexReturn = result.fund.indexId ? calculateIndexReturn(entry.date, result) : 0;

    return {
      date: entry.date,
      strategyReturn,
      fundReturn,
      indexReturn,
    };
  });
}

/**
 * 计算指数收益率
 * 如果有指数数据，计算指数收益率；否则返回0
 */
function calculateIndexReturn(date: string, result: BacktestResult): number {
  // 这里应该实现从指数数据计算收益率的逻辑
  // 目前暂时返回0，后续可以根据实际数据实现
  return 0;
}
