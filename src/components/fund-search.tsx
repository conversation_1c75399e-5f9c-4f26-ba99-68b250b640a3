"use client";

import { useState, useTransition } from "react";

import { validateFundAction } from "@/lib/actions";
import type { Fund } from "@/types/fund";

interface FundSearchProperties {
  onFundSelect: (fund: Fund) => void;
  selectedFund?: Fund;
  placeholder?: string;
  className?: string;
  // 添加测试中使用的属性
  funds?: Fund[];
  isLoading?: boolean;
  recentSearches?: string[];
  showAdvancedFilter?: boolean;
  error?: string;
  multiSelect?: boolean;
  showDetails?: boolean;
  showFavorites?: boolean;
}

export default function FundSearch({
  onFundSelect,
  selectedFund,
  placeholder = "搜索基金代码或名称...",
  className = "",
}: FundSearchProperties) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<Fund[]>([]);
  const [isOpen, setIsOpen] = useState(false);
  const [validationStatus, setValidationStatus] = useState<
    "idle" | "validating" | "valid" | "invalid"
  >("idle");
  const [isLoading, setIsLoading] = useState(false);
  const [, startTransition] = useTransition();

  // 搜索基金
  const handleSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setResults([]);
      setValidationStatus("idle");
      return;
    }

    setIsLoading(true);
    startTransition(async () => {
      try {
        // 使用新的CSRC API搜索基金
        const response = await fetch(
          `/api/fund/search?q=${encodeURIComponent(searchQuery)}`
        );
        const result = await response.json();

        if (result.success) {
          setResults(result.data);
        } else {
          console.error("搜索基金失败:", result.error);
          setResults([]);
        }

        // 如果查询看起来像基金代码，验证其有效性
        if (/^\d{6}$/.test(searchQuery)) {
          setValidationStatus("validating");
          const isValid = await validateFundAction(searchQuery);
          setValidationStatus(isValid ? "valid" : "invalid");
        } else {
          setValidationStatus("idle");
        }
      } catch (error) {
        console.error("搜索基金失败:", error);
        setResults([]);
        setValidationStatus("invalid");
      } finally {
        setIsLoading(false);
      }
    });
  };

  // 选择基金
  const handleFundSelect = (fund: Fund) => {
    setQuery(fund.name);
    setIsOpen(false);
    onFundSelect(fund);
  };

  // 输入变化处理
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setQuery(value);
    setIsOpen(true);

    // 使用防抖搜索
    const timer = setTimeout(() => {
      handleSearch(value);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  };

  // 获取验证状态样式
  const getValidationStyle = () => {
    switch (validationStatus) {
      case "validating": {
        return "border-yellow-300 bg-yellow-50";
      }
      case "valid": {
        return "border-green-300 bg-green-50";
      }
      case "invalid": {
        return "border-red-300 bg-red-50";
      }
      default: {
        return "border-gray-300";
      }
    }
  };

  // 获取验证状态图标
  const getValidationIcon = () => {
    switch (validationStatus) {
      case "validating": {
        return (
          <div className="animate-spin rounded-full h-4 w-4 border-2 border-yellow-500 border-t-transparent" />
        );
      }
      case "valid": {
        return (
          <svg
            className="h-4 w-4 text-green-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 13l4 4L19 7"
            />
          </svg>
        );
      }
      case "invalid": {
        return (
          <svg
            className="h-4 w-4 text-red-500"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        );
      }
      default: {
        return null;
      }
    }
  };

  // 渲染空状态
  const renderEmptyState = () => {
    if (!query.trim()) return null;

    const message = validationStatus === "invalid" ? "未找到匹配的基金" : "暂无搜索结果";

    return (
      <div className="px-3 py-2 text-gray-500 text-center">
        {message}
      </div>
    );
  };

  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={() => {
            setIsOpen(true);
          }}
          placeholder={placeholder}
          className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${getValidationStyle()}`}
        />

        {/* 验证状态图标 */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {getValidationIcon()}
        </div>
      </div>

      {/* 搜索结果下拉列表 */}
      {isOpen && (query.trim() || results.length > 0) ? (
        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {(() => {
            if (isLoading) {
              return (
                <div className="px-3 py-2 text-gray-500 text-center">
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-blue-500 border-t-transparent" />
                    <span>搜索中...</span>
                  </div>
                </div>
              );
            } else if (results.length > 0) {
              return results.map((fund) => (
              <div
                key={fund.id}
                onClick={() => {
                  handleFundSelect(fund);
                }}
                className="px-3 py-2 hover:bg-gray-100 cursor-pointer border-b border-gray-100 last:border-b-0"
              >
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium text-gray-900">{fund.name}</div>
                    <div className="text-sm text-gray-500">
                      代码: {fund.code} | 类型: {getFundTypeLabel(fund.type)}
                    </div>
                  </div>
                  {selectedFund?.id === fund.id && (
                    <svg
                      className="h-4 w-4 text-blue-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  )}
                </div>
              </div>
              ));
            } else {
              return renderEmptyState();
            }
          })()}
        </div>
      ) : null}

      {/* 点击外部关闭下拉列表 */}
      {isOpen ? (
        <div
          className="fixed inset-0 z-0"
          onClick={() => {
            setIsOpen(false);
          }}
        />
      ) : null}
    </div>
  );
}

// 基金类型标签映射
function getFundTypeLabel(type: string): string {
  const typeMap: Record<string, string> = {
    stock: "股票型",
    bond: "债券型",
    hybrid: "混合型",
    index: "指数型",
    money: "货币型",
  };

  return typeMap[type] || type;
}
