import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { vi } from "vitest";

import StrategySelector from "./StrategySelector";
import type { Strategy } from "@/types/fund";

describe("StrategySelector", () => {
  const mockOnStrategySelect = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  const mockStrategy: Strategy = {
    id: "fixed_amount",
    name: "定投策略",
    description: "每月固定金额投资",
    riskLevel: "low",
    complexity: "beginner",
    parameterSchema: {},
  };

  const valueAveragingStrategy: Strategy = {
    id: "value_averaging",
    name: "价值平均策略",
    description: "根据目标增长率调整投资金额",
    riskLevel: "medium",
    complexity: "intermediate",
    parameterSchema: {},
  };

  const smartFixedStrategy: Strategy = {
    id: "smart_fixed",
    name: "智能定投",
    description: "基于估值指标调整投资金额",
    riskLevel: "medium",
    complexity: "intermediate",
    parameterSchema: {},
  };

  const gridTradingStrategy: Strategy = {
    id: "grid_trading",
    name: "网格交易",
    description: "在价格区间内设置买卖网格",
    riskLevel: "high",
    complexity: "advanced",
    parameterSchema: {},
  };

  const momentumStrategy: Strategy = {
    id: "momentum",
    name: "动量策略",
    description: "基于价格趋势进行投资",
    riskLevel: "high",
    complexity: "advanced",
    parameterSchema: {},
  };

  const meanReversionStrategy: Strategy = {
    id: "mean_reversion",
    name: "均值回归",
    description: "基于移动平均线偏离度",
    riskLevel: "medium",
    complexity: "advanced",
    parameterSchema: {},
  };

  it("应该渲染所有策略选项", () => {
    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("定投策略")).toBeInTheDocument();
    expect(screen.getByText("价值平均策略")).toBeInTheDocument();
    expect(screen.getByText("智能定投")).toBeInTheDocument();
    expect(screen.getByText("网格交易")).toBeInTheDocument();
    expect(screen.getByText("动量策略")).toBeInTheDocument();
    expect(screen.getByText("均值回归")).toBeInTheDocument();
  });

  it("应该高亮显示选中的策略", () => {
    render(
      <StrategySelector
        selectedStrategy={valueAveragingStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    const selectedCard = screen.getByText("价值平均策略").closest("div");
    expect(selectedCard).toHaveClass("border-blue-500");
  });

  it("应该在点击策略卡片时调用onStrategySelect", async () => {
    const user = userEvent.setup();
    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    await user.click(screen.getByText("网格交易"));
    expect(mockOnStrategySelect).toHaveBeenCalled();
  });

  it("应该显示策略描述", () => {
    render(
      <StrategySelector
        selectedStrategy={smartFixedStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("基于估值指标调整投资金额")).toBeInTheDocument();
  });

  it("应该显示风险等级标签", () => {
    render(
      <StrategySelector
        selectedStrategy={gridTradingStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("高风险")).toBeInTheDocument();
  });

  it("应该显示复杂度标签", () => {
    render(
      <StrategySelector
        selectedStrategy={momentumStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("高级")).toBeInTheDocument();
  });

  it("应该支持键盘导航", async () => {
    const user = userEvent.setup();
    render(
      <StrategySelector
        selectedStrategy={meanReversionStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    const firstCard = screen.getByText("定投策略").closest("div");
    if (firstCard) {
      firstCard.focus();
      await user.keyboard("{ArrowRight}");
      await user.keyboard("{Enter}");
      expect(mockOnStrategySelect).toHaveBeenCalled();
    }
  });

  it("应该在没有选中策略时正常渲染", () => {
    render(
      <StrategySelector
        selectedStrategy={null}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("定投策略")).toBeInTheDocument();
  });

  it("应该支持自定义类名", () => {
    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
        className="custom-class"
      />
    );

    const container = screen.getByText("定投策略").closest(".custom-class");
    expect(container).toBeInTheDocument();
  });

  it("应该支持自定义标题", () => {
    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
        title="自定义标题"
      />
    );

    expect(screen.getByText("自定义标题")).toBeInTheDocument();
  });

  it("应该支持禁用状态", () => {
    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
        disabled={true}
      />
    );

    const cards = screen.getAllByRole("button");
    for (const card of cards) {
      expect(card).toHaveAttribute("disabled");
    }
  });

  it("应该支持紧凑模式", () => {
    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
        compact={true}
      />
    );

    const container = screen.getByText("定投策略").closest("div");
    expect(container).toHaveClass("p-2");
  });

  it("应该支持自定义策略列表", () => {
    const customStrategies = [mockStrategy, valueAveragingStrategy];

    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
        strategies={customStrategies}
      />
    );

    expect(screen.getByText("定投策略")).toBeInTheDocument();
    expect(screen.getByText("价值平均策略")).toBeInTheDocument();
    expect(screen.queryByText("智能定投")).not.toBeInTheDocument();
  });

  it("应该处理无效的策略类型", () => {
    const invalidStrategy = {
      ...mockStrategy,
      id: "custom_strategy" as any,
    };

    render(
      <StrategySelector
        selectedStrategy={invalidStrategy}
        onStrategySelect={mockOnStrategySelect}
      />
    );

    expect(screen.getByText("定投策略")).toBeInTheDocument();
  });

  it("应该支持搜索过滤", async () => {
    const user = userEvent.setup();
    render(
      <StrategySelector
        selectedStrategy={mockStrategy}
        onStrategySelect={mockOnStrategySelect}
        showSearch={true}
      />
    );

    const searchInput = screen.getByPlaceholderText("搜索策略...");
    await user.type(searchInput, "网格");

    expect(screen.getByText("网格交易")).toBeInTheDocument();
    expect(screen.queryByText("定投策略")).not.toBeInTheDocument();
  });
});
