"use client";

import { useState } from "react";

import { getStrategies } from "@/lib/strategies";
import type { Strategy } from "@/types/fund";

// 辅助函数，移到组件外部以避免 ESLint 警告
function getRiskLevelColor(level: Strategy["riskLevel"]) {
  const colors = {
    low: "bg-green-100 text-green-800",
    medium: "bg-yellow-100 text-yellow-800",
    high: "bg-red-100 text-red-800",
  };
  return colors[level];
}

function getRiskLevelLabel(level: Strategy["riskLevel"]) {
  const labels = {
    low: "低风险",
    medium: "中风险",
    high: "高风险",
  };
  return labels[level];
}

function getComplexityColor(complexity: Strategy["complexity"]) {
  const colors = {
    beginner: "bg-blue-100 text-blue-800",
    intermediate: "bg-purple-100 text-purple-800",
    advanced: "bg-gray-100 text-gray-800",
  };
  return colors[complexity];
}

function getComplexityLabel(complexity: Strategy["complexity"]) {
  const labels = {
    beginner: "初级",
    intermediate: "中级",
    advanced: "高级",
  };
  return labels[complexity];
}

interface StrategySelectorProperties {
  selectedStrategy: Strategy | null;
  onStrategySelect: (strategy: Strategy) => void;
  className?: string;
  title?: string;
  disabled?: boolean;
  compact?: boolean;
  strategies?: Strategy[];
  showSearch?: boolean;
}

export default function StrategySelector({
  selectedStrategy,
  onStrategySelect,
}: StrategySelectorProperties) {
  const [expandedStrategy, setExpandedStrategy] = useState<string | null>(null);
  const strategies = getStrategies();

  // 切换策略详情展开状态
  const toggleExpanded = (id: string) => {
    if (expandedStrategy === id) {
      setExpandedStrategy(null);
    } else {
      setExpandedStrategy(id);
    }
  };

  return (
    <div className="space-y-4">
      <h2 className="text-lg font-medium text-gray-900">选择投资策略</h2>
      <div className="space-y-2">
        {strategies.map((strategy) => (
          <div
            key={strategy.id}
            onClick={() => {
              onStrategySelect(strategy);
            }}
            className={`p-4 border rounded-lg cursor-pointer transition-all ${
              selectedStrategy?.id === strategy.id
                ? "border-blue-500 bg-blue-50"
                : "border-gray-200 hover:border-blue-300"
            }`}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-2">
                <div className="font-medium text-gray-900">{strategy.name}</div>
                <span
                  className={`text-xs px-2 py-0.5 rounded-full ${getRiskLevelColor(
                    strategy.riskLevel
                  )}`}
                >
                  {getRiskLevelLabel(strategy.riskLevel)}
                </span>
                <span
                  className={`text-xs px-2 py-0.5 rounded-full ${getComplexityColor(
                    strategy.complexity
                  )}`}
                >
                  {getComplexityLabel(strategy.complexity)}
                </span>
              </div>
              {selectedStrategy?.id === strategy.id && (
                <svg
                  className="h-5 w-5 text-blue-500"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              )}
            </div>

            <div className="mt-2">
              <p className="text-sm text-gray-600">
                {(() => {
                  if (expandedStrategy === strategy.id) {
                    return strategy.description;
                  }
                  
                  const truncated = strategy.description.slice(0, 100);
                  const suffix = strategy.description.length > 100 ? "..." : "";
                  return `${truncated}${suffix}`;
                })()}
              </p>

              {/* 展开/收起按钮 */}
              <button
                onClick={(event) => {
                  event.stopPropagation();
                  toggleExpanded(strategy.id);
                }}
                className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
              >
                {expandedStrategy === strategy.id ? "收起详情" : "查看详情"}
                <svg
                  className={`w-4 h-4 transition-transform ${
                    expandedStrategy === strategy.id ? "rotate-180" : ""
                  }`}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
