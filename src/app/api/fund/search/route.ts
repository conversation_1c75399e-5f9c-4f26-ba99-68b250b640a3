import { NextRequest, NextResponse } from "next/server";

interface CSRCFundSearchParams {
  fundName: string;
  fundType?: string;
  pageNum?: number;
  pageSize?: number;
}

interface CSRCFundItem {
  fund: {
    fundCode: string;
    fundName: string;
    fundType: string;
    fundCompanyShortName: string;
  };
  valuationDate: string;
}

interface CSRCApiResponse {
  aaData: CSRCFundItem[];
  iTotalRecords: number;
  iTotalDisplayRecords: number;
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const pageNum = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("size") || "20");

    if (!query) {
      return NextResponse.json(
        { success: false, error: "搜索关键词不能为空" },
        { status: 400 }
      );
    }

    // 构建CSRC API请求参数
    const aoData = [
      { name: "sEcho", value: 1 },
      { name: "iColumns", value: 5 },
      { name: "sColumns", value: ",,,,," },
      { name: "iDisplayStart", value: (pageNum - 1) * pageSize },
      { name: "iDisplayLength", value: pageSize },
      { name: "mDataProp_0", value: "fund" },
      { name: "mDataProp_1", value: "fund" },
      { name: "mDataProp_2", value: "fund" },
      { name: "mDataProp_3", value: "fund" },
      { name: "mDataProp_4", value: "valuationDate" },
      { name: "fundType", value: "6020-6010" }, // 所有基金类型
      { name: "fundCompanyShortName", value: "" },
      { name: "fundCode", value: "" },
      { name: "fundName", value: encodeURIComponent(query) },
      { name: "startDate", value: new Date().toISOString().split("T")[0] },
      { name: "endDate", value: new Date().toISOString().split("T")[0] },
    ];

    const url = `http://eid.csrc.gov.cn/fund/disclose/getPublicFundJZInfoMore.do?aoData=${encodeURIComponent(JSON.stringify(aoData))}&_=${Date.now()}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        Accept: "application/json, text/javascript, */*; q=0.01",
        "Accept-Language": "en-GB,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,en-US;q=0.6",
        "Cache-Control": "no-cache",
        Connection: "keep-alive",
        "Content-Type": "application/json",
        DNT: "1",
        Pragma: "no-cache",
        Referer: "http://eid.csrc.gov.cn/fund/disclose/index.html",
        "User-Agent":
          "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "X-Requested-With": "XMLHttpRequest",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: CSRCApiResponse = await response.json();

    // 转换为项目内部格式
    const funds = data.aaData.map((item, index) => ({
      id: `${item.fund.fundCode}-${index}`,
      name: item.fund.fundName,
      code: item.fund.fundCode,
      type: mapFundType(item.fund.fundType),
      company: item.fund.fundCompanyShortName,
      description: `${item.fund.fundCompanyShortName}管理的${mapFundType(item.fund.fundType)}基金`,
    }));

    return NextResponse.json({
      success: true,
      data: funds,
      total: data.iTotalRecords,
      page: pageNum,
      size: pageSize,
    });
  } catch (error) {
    console.error("基金搜索失败:", error);
    return NextResponse.json(
      { success: false, error: "基金搜索失败" },
      { status: 500 }
    );
  }
}

function mapFundType(csrcType: string): "stock" | "bond" | "hybrid" | "index" | "money" {
  // 根据CSRC基金类型映射到项目内部类型
  const typeMap: Record<string, "stock" | "bond" | "hybrid" | "index" | "money"> = {
    "股票型": "stock",
    "债券型": "bond",
    "混合型": "hybrid",
    "指数型": "index",
    "货币型": "money",
  };

  return typeMap[csrcType] || "hybrid";
}
