import { NextRequest, NextResponse } from "next/server";

interface CSIndexSearchRequest {
  sorter: {
    sortField: string | null;
    sortOrder: string | null;
  };
  pager: {
    pageNum: number;
    pageSize: number;
  };
  searchInput: string;
  indexFilter: {
    ifCustomized: null;
    ifTracked: null;
    ifWeightCapped: null;
    indexCompliance: null;
    hotSpot: null;
    indexClassify: null;
    currency: null;
    region: null;
    indexSeries: null;
    undefined: null;
  };
}

interface CSIndexItem {
  indexCode: string;
  indexName: string;
  indexNameEn: string;
  publishDate: string;
  baseDate: string;
  basePoint: number;
  indexType: string;
  indexClassify: string;
  currency: string;
  region: string;
}

interface CSIndexApiResponse {
  code: string;
  message: string;
  data: {
    data: CSIndexItem[];
    total: number;
    pageNum: number;
    pageSize: number;
  };
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get("q");
    const pageNum = parseInt(searchParams.get("page") || "1");
    const pageSize = parseInt(searchParams.get("size") || "10");

    if (!query) {
      return NextResponse.json(
        { success: false, error: "搜索关键词不能为空" },
        { status: 400 }
      );
    }

    // 构建CSIndex API请求体
    const requestBody: CSIndexSearchRequest = {
      sorter: {
        sortField: "null",
        sortOrder: null,
      },
      pager: {
        pageNum,
        pageSize,
      },
      searchInput: query,
      indexFilter: {
        ifCustomized: null,
        ifTracked: null,
        ifWeightCapped: null,
        indexCompliance: null,
        hotSpot: null,
        indexClassify: null,
        currency: null,
        region: null,
        indexSeries: null,
        undefined: null,
      },
    };

    const response = await fetch(
      "https://www.csindex.com.cn/csindex-home/index-list/query-index-item",
      {
        method: "POST",
        headers: {
          Accept: "application/json, text/plain, */*",
          "Accept-Language": "en-GB,en;q=0.9,zh-CN;q=0.8,zh;q=0.7,en-US;q=0.6",
          "Cache-Control": "no-cache",
          Connection: "keep-alive",
          "Content-Type": "application/json;charset=UTF-8",
          DNT: "1",
          Origin: "https://www.csindex.com.cn",
          Pragma: "no-cache",
          Referer: "https://www.csindex.com.cn/",
          "Sec-Fetch-Dest": "empty",
          "Sec-Fetch-Mode": "cors",
          "Sec-Fetch-Site": "same-origin",
          "User-Agent":
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0",
          "sec-ch-ua":
            '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": '"macOS"',
        },
        body: JSON.stringify(requestBody),
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: CSIndexApiResponse = await response.json();

    if (data.code !== "200") {
      throw new Error(`API错误: ${data.message}`);
    }

    // 转换为项目内部格式
    const indices = data.data.data.map((item) => ({
      id: item.indexCode,
      name: item.indexName,
      code: item.indexCode,
      description: `${item.indexClassify} | 基准日期: ${item.baseDate} | 基准点数: ${item.basePoint}`,
      type: item.indexType,
      classify: item.indexClassify,
      currency: item.currency,
      region: item.region,
      publishDate: item.publishDate,
      baseDate: item.baseDate,
      basePoint: item.basePoint,
    }));

    return NextResponse.json({
      success: true,
      data: indices,
      total: data.data.total,
      page: data.data.pageNum,
      size: data.data.pageSize,
    });
  } catch (error) {
    console.error("指数搜索失败:", error);
    return NextResponse.json(
      { success: false, error: "指数搜索失败" },
      { status: 500 }
    );
  }
}
