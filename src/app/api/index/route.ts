// 指数数据API路由

import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

import type { CSIndexApiResponse } from "@/lib/api/types";
import {
  buildCSIndexApiUrl,
  parseCSIndexData,
  withRetry,
  withTimeout,
} from "@/lib/api/utilities";
import type { IndexData } from "@/types/fund";

// 服务端直接调用CSIndex API，避免CORS问题
async function fetchIndexDataFromCSIndex(
  indexCode: string,
  startDate: string,
  endDate: string
): Promise<IndexData[]> {
  const url = buildCSIndexApiUrl(indexCode, startDate, endDate);

  const fetchWithRetry = () =>
    withRetry(
      async () => {
        const response = await fetch(url, {
          method: "GET",
          headers: {
            "User-Agent":
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36",
            Accept: "application/json, text/plain, */*",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
            "Cache-Control": "no-cache",
            Pragma: "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.code !== "200") {
          throw new Error(`API错误: ${data.msg}`);
        }

        return data as CSIndexApiResponse;
      },
      { maxRetries: 3, delay: 1000 }
    );

  const rawData = await withTimeout(fetchWithRetry(), 15_000);
  const parsedData = parseCSIndexData(rawData);

  // 转换为项目内部格式
  return parsedData.map((item) => ({
    date: item.time,
    value: item.close,
    change: item.range,
    changePercent: item.ratio,
  }));
}

// 格式化日期为CSIndex API要求的格式 (YYYYMMDD)
function formatDateForCSIndex(dateString: string): string {
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}${month}${day}`;
}

// 计算日期范围
function calculateDateRange(startDate?: string, endDate?: string) {
  const now = new Date();
  const oneYearAgo = new Date(
    now.getFullYear() - 1,
    now.getMonth(),
    now.getDate()
  );

  const defaultStartDate = startDate || oneYearAgo.toISOString().split("T")[0];
  const defaultEndDate = endDate || now.toISOString().split("T")[0];

  return {
    formattedStartDate: formatDateForCSIndex(defaultStartDate),
    formattedEndDate: formatDateForCSIndex(defaultEndDate),
  };
}

// GET /api/index?code=000300&startDate=2024-01-01&endDate=2024-12-31
export async function GET(request: NextRequest) {
  try {
    const { searchParameters } = new URL(request.url);
    const indexCode = searchParameters.get("code");
    const startDate = searchParameters.get("startDate");
    const endDate = searchParameters.get("endDate");

    if (!indexCode) {
      return NextResponse.json({ error: "指数代码不能为空" }, { status: 400 });
    }

    // 验证指数代码格式
    if (!/^\d{6}$/.test(indexCode)) {
      return NextResponse.json(
        { error: "指数代码格式错误，应为6位数字" },
        { status: 400 }
      );
    }

    // 计算日期范围
    const { formattedStartDate, formattedEndDate } = calculateDateRange(
      startDate || undefined,
      endDate || undefined
    );

    // 服务端获取指数数据
    const indexData: IndexData[] = await fetchIndexDataFromCSIndex(
      indexCode,
      formattedStartDate,
      formattedEndDate
    );

    return NextResponse.json({
      success: true,
      data: indexData,
      meta: {
        indexCode,
        startDate,
        endDate,
        count: indexData.length,
      },
    });
  } catch (error) {
    console.error("获取指数数据失败:", error);

    const errorMessage = error instanceof Error ? error.message : "未知错误";

    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        code: "INDEX_DATA_ERROR",
      },
      { status: 500 }
    );
  }
}
