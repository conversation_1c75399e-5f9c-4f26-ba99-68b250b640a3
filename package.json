{"name": "fund-strategy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "lint:check": "next lint --max-warnings 0", "format": "prettier --write .", "format:check": "prettier --check .", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:ui": "vitest --ui", "knip": "knip", "knip:production": "knip --production", "knip:fix": "knip --fix", "knip:watch": "knip --watch"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tooltip": "^1.2.7", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "next": "15.3.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.57.0", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@eslint/js": "^9.28.0", "@next/eslint-plugin-next": "^15.3.3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@typescript-eslint/parser": "^8.33.1", "@vitejs/plugin-react": "^4.5.1", "@vitest/coverage-v8": "3.2.2", "eslint": "^9", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-import-x": "^4.15.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-sonarjs": "^3.0.2", "eslint-plugin-unicorn": "^59.0.1", "husky": "^9.1.7", "jsdom": "^26.1.0", "knip": "^5.60.2", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5.8.3", "typescript-eslint": "^8.33.1", "vitest": "^3.2.2"}}