import { FlatCompat } from "@eslint/eslintrc";
import pluginPromise from "eslint-plugin-promise";
import eslintPluginUnicorn from "eslint-plugin-unicorn";
import sonarjs from "eslint-plugin-sonarjs";

const compat = new FlatCompat({
  // import.meta.dirname is available after Node.js v20.11.0
  baseDirectory: import.meta.dirname,
});

const eslintConfig = [
  ...compat.config({
    extends: ["next/core-web-vitals", "next/typescript", "prettier"],
  }),
  eslintPluginUnicorn.configs.recommended,

  pluginPromise.configs["flat/recommended"],
  sonarjs.configs.recommended,
  {
    rules: {
      "unicorn/no-nested-ternary": "off",
      "unicorn/no-null": "off",
      "unicorn/prefer-spread": "off",
      "unicorn/prefer-global-this": "off",
      "unicorn/number-literal-case": "off",
      "unicorn/catch-error-name": "off",
      "unicorn/no-object-as-default-parameter": "off",
      "unicorn/no-anonymous-default-export": "off",
      "unicorn/no-useless-undefined": "off",
    },
  },
  {
    files: ["**/*.{test,spec}.{ts,tsx}", "**/__tests__/**/*.{ts,tsx}"],
    rules: {
      // Allow test-specific patterns
      "@typescript-eslint/no-explicit-any": "off",
      "@typescript-eslint/no-unsafe-assignment": "off",
      "@typescript-eslint/no-unsafe-member-access": "off",
      "@typescript-eslint/no-unsafe-call": "off",
      "@typescript-eslint/no-unsafe-return": "off",
      "sonarjs/no-duplicate-string": "off",
    },
  },
];

export default eslintConfig;
