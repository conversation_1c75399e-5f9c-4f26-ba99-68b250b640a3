import type { KnipConfig } from "knip";

const config: KnipConfig = {
  // Entry points for the application
  entry: [
    // Next.js app directory entry points
    "src/app/**/{page,layout,loading,error,not-found,global-error}.{js,jsx,ts,tsx}",
    "src/app/**/route.{js,ts}", // API routes
    "src/app/globals.css", // Global styles

    // Configuration files that are actually used
    "postcss.config.{js,mjs}",
  ],

  // Project-specific entry points
  project: [
    // All TypeScript and JavaScript files in src
    "src/**/*.{js,jsx,ts,tsx}",
    // Configuration files
    "*.{js,jsx,ts,tsx,mjs}",
    // Exclude test files from project analysis (they're handled separately)
    "!**/*.{test,spec}.{js,jsx,ts,tsx}",
  ],

  // Ignore patterns - files/directories to exclude from analysis
  ignore: [
    // Build outputs
    ".next/**",
    "out/**",
    "dist/**",
    "build/**",

    // Dependencies
    "node_modules/**",

    // Coverage and test outputs
    "coverage/**",

    // Environment and lock files
    ".env*",
    "pnpm-lock.yaml",
    "package-lock.json",
    "yarn.lock",

    // Documentation and markdown files
    "**/*.md",
    "docs/**",

    // Public assets (these are referenced by path, not imports)
    "public/**",

    // Git and IDE files
    ".git/**",
    ".vscode/**",
    ".idea/**",
  ],

  // Exclude specific dependencies from unused dependency checks
  ignoreDependencies: [
    // Tailwind CSS (used via classes, not imports)
    "tailwindcss",
    "postcss",

    // Development tools
    "husky",
    "lint-staged",
  ],

  // Exclude specific files from unused file checks
  ignoreExportsUsedInFile: {
    // Allow unused exports in these file patterns
    interface: true, // TypeScript interfaces
    type: true, // TypeScript types
  },
};

export default config;
